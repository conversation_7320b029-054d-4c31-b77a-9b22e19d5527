// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/CommonUIWidgetBase.h"
#include "Components/Widget.h"

UCommonUIWidgetBase::UCommonUIWidgetBase(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	bEnableCustomFocusRestore = true;
	DesiredFocusTarget = nullptr;
}

void UCommonUIWidgetBase::NativeConstruct()
{
	Super::NativeConstruct();
	
	UE_LOG(LogTemp, Log, TEXT("CommonUIWidgetBase: Constructing %s"), *GetClass()->GetName());
}

void UCommonUIWidgetBase::NativeOnActivated()
{
	Super::NativeOnActivated();
	
	UE_LOG(LogTemp, Log, TEXT("CommonUIWidgetBase: Activated %s"), *GetClass()->GetName());
	
	// Store the currently focused widget for potential restoration
	if (bEnableCustomFocusRestore)
	{
		PreviouslyFocusedWidget = GetCurrentlyFocusedWidget();
	}
	
	// Call Blueprint event
	BP_OnWidgetActivated();
	
	// Set initial focus using Blueprint event
	BP_OnSetInitialFocus();
}

void UCommonUIWidgetBase::NativeOnDeactivated()
{
	Super::NativeOnDeactivated();
	
	UE_LOG(LogTemp, Log, TEXT("CommonUIWidgetBase: Deactivated %s"), *GetClass()->GetName());
	
	// Call Blueprint event
	BP_OnWidgetDeactivated();
	
	// Restore focus to the previously focused widget if enabled
	if (bEnableCustomFocusRestore && PreviouslyFocusedWidget.IsValid())
	{
		SetFocusToWidget(PreviouslyFocusedWidget.Get());
		PreviouslyFocusedWidget.Reset();
	}
}

bool UCommonUIWidgetBase::NativeOnHandleBackAction()
{
	UE_LOG(LogTemp, Log, TEXT("CommonUIWidgetBase: Back action triggered for %s"), *GetClass()->GetName());

	// Call the parent implementation which handles BP_OnHandleBackAction
	if (Super::NativeOnHandleBackAction())
	{
		return true; // Parent/Blueprint handled it
	}

	// Default behavior: deactivate the widget
	DeactivateWidget();
	return true;
}

UWidget* UCommonUIWidgetBase::NativeGetDesiredFocusTarget() const
{
	// Return the desired focus target if set
	if (DesiredFocusTarget)
	{
		return DesiredFocusTarget;
	}
	
	// Fall back to the base class implementation
	return Super::NativeGetDesiredFocusTarget();
}

void UCommonUIWidgetBase::SetFocusToWidget(UWidget* Widget)
{
	if (Widget && Widget->GetVisibility() == ESlateVisibility::Visible)
	{
		Widget->SetFocus();
		UE_LOG(LogTemp, Log, TEXT("CommonUIWidgetBase: Focus set to widget %s"), *Widget->GetClass()->GetName());
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("CommonUIWidgetBase: Cannot set focus to widget - widget is null or not visible"));
	}
}

UWidget* UCommonUIWidgetBase::GetCurrentlyFocusedWidget() const
{
	// Get the currently focused widget from the slate application
	if (FSlateApplication::IsInitialized())
	{
		TSharedPtr<SWidget> FocusedWidget = FSlateApplication::Get().GetKeyboardFocusedWidget();
		if (FocusedWidget.IsValid())
		{
			// Try to find the UWidget that corresponds to this SWidget
			// This is a simplified approach - in practice, you might need more sophisticated logic
			return nullptr; // For now, return nullptr as this requires more complex slate integration
		}
	}
	
	return nullptr;
}
