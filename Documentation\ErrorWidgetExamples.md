# Error Widget Blueprint Examples

This document provides copy-paste Blueprint node setups for common error widget scenarios.

## Basic Error Scenarios

### 1. Simple Error Message
**Use Case:** Show a basic error with just an OK button

**Blueprint Nodes:**
```
Event [Trigger] 
└─ Show Simple Error Popup
   ├─ Error Message: "Failed to save game"
   └─ World Context Object: Self
```

**When to Use:** File errors, save/load failures, simple notifications

---

### 2. Network Connection Error
**Use Case:** Network-related errors with consistent styling

**Blueprint Nodes:**
```
Event On Network Error
└─ Show Network Error Popup
   ├─ Error Message: "Unable to connect to server. Please check your internet connection."
   └─ World Context Object: Self
```

**When to Use:** Connection timeouts, server unavailable, network failures

---

### 3. Lobby/Multiplayer Error
**Use Case:** Lobby-specific errors (joining, creating, etc.)

**Blueprint Nodes:**
```
Event On Lobby Join Failed
└─ Show Lobby Error Popup
   ├─ Error Message: "Failed to join lobby. The lobby may be full or no longer exist."
   └─ World Context Object: Self
```

**When to Use:** Lobby full, lobby not found, join failures

---

## Confirmation Dialogs

### 4. Delete Confirmation
**Use Case:** Confirm destructive actions

**Blueprint Nodes:**
```
Event On Delete Button Clicked
├─ Show Confirmation Dialog
│  ├─ Title: "Delete Save File"
│  ├─ Message: "This action cannot be undone. Are you sure you want to delete this save file?"
│  ├─ World Context Object: Self
│  ├─ Confirm Text: "Delete"
│  └─ Cancel Text: "Keep"
├─ Return Value → Bind Event to On Error Confirm
│  └─ Custom Event: Actually Delete Save
└─ Return Value → Bind Event to On Error Cancel
   └─ Custom Event: Cancel Delete (optional)
```

**When to Use:** Delete saves, reset settings, destructive actions

---

### 5. Quit Game Confirmation
**Use Case:** Confirm when player wants to quit

**Blueprint Nodes:**
```
Event On Quit Button Clicked
├─ Show Confirmation Dialog
│  ├─ Title: "Quit Game"
│  ├─ Message: "Are you sure you want to quit? Any unsaved progress will be lost."
│  ├─ World Context Object: Self
│  ├─ Confirm Text: "Quit"
│  └─ Cancel Text: "Stay"
├─ Return Value → Bind Event to On Error Confirm
│  └─ Quit Game
└─ Return Value → Bind Event to On Error Cancel
   └─ [Do nothing - just close dialog]
```

**When to Use:** Quit game, exit to menu, leave lobby

---

## Advanced Scenarios

### 6. Network Error with Retry Option
**Use Case:** Network error that allows retry

**Blueprint Nodes:**
```
Event On Connection Failed
├─ Show Error Popup Advanced
│  ├─ Error Head: "Connection Failed"
│  ├─ Error Body: "Unable to connect to the game server. Would you like to try again?"
│  ├─ World Context Object: Self
│  ├─ Show Confirm Button: True
│  ├─ Show Cancel Button: True
│  ├─ Confirm Text: "Retry"
│  └─ Cancel Text: "Offline Mode"
├─ Return Value → Bind Event to On Error Confirm
│  └─ Custom Event: Retry Connection
└─ Return Value → Bind Event to On Error Cancel
   └─ Custom Event: Enter Offline Mode
```

**Custom Events:**
```
Custom Event: Retry Connection
├─ Delay (2.0 seconds)
├─ Attempt Connection
├─ Branch (Success?)
│  ├─ True: [Continue to game]
│  └─ False: [Show error again]
```

---

### 7. Save File Corruption Error
**Use Case:** Critical error with multiple options

**Blueprint Nodes:**
```
Event On Save Corruption Detected
├─ Show Error Popup Advanced
│  ├─ Error Head: "Save File Corrupted"
│  ├─ Error Body: "Your save file appears to be corrupted. You can try to recover it or start a new game."
│  ├─ World Context Object: Self
│  ├─ Show Confirm Button: True
│  ├─ Show Cancel Button: True
│  ├─ Confirm Text: "Try Recovery"
│  └─ Cancel Text: "New Game"
├─ Return Value → Bind Event to On Error Confirm
│  └─ Custom Event: Attempt Save Recovery
└─ Return Value → Bind Event to On Error Cancel
   └─ Custom Event: Start New Game
```

---

### 8. Version Mismatch Error
**Use Case:** Game version incompatibility

**Blueprint Nodes:**
```
Event On Version Mismatch
├─ Show Error Popup Advanced
│  ├─ Error Head: "Version Mismatch"
│  ├─ Error Body: "Your game version is incompatible with the server. Please update your game."
│  ├─ World Context Object: Self
│  ├─ Show Confirm Button: True
│  ├─ Show Cancel Button: True
│  ├─ Confirm Text: "Check for Updates"
│  └─ Cancel Text: "Play Offline"
├─ Return Value → Bind Event to On Error Confirm
│  └─ Open URL: "steam://nav/games/details/[YourGameID]"
└─ Return Value → Bind Event to On Error Cancel
   └─ Custom Event: Enter Offline Mode
```

---

## Custom Widget Behavior

### 9. Custom Button Actions (In Your Error Widget Blueprint)
**Use Case:** Override default button behavior

**Override BP On Confirm Clicked:**
```
Event BP On Confirm Clicked
├─ Print String: "User confirmed the action"
├─ Play Sound 2D: ConfirmSound
├─ [Your custom logic here]
└─ Close Error Widget (if you want to close manually)
```

**Override BP On Cancel Clicked:**
```
Event BP On Cancel Clicked
├─ Print String: "User cancelled the action"
├─ Play Sound 2D: CancelSound
├─ [Your custom logic here]
└─ Close Error Widget (if you want to close manually)
```

---

### 10. Custom Back Button Behavior (In Your Error Widget Blueprint)
**Use Case:** Handle gamepad back button specially

**Override BP On Handle Back Action:**
```
Event BP On Handle Back Action
├─ Branch (Cancel Button Visible?)
│  ├─ True: On Cancel Clicked
│  └─ False: On Confirm Clicked
└─ Return Value: True
```

---

### 11. Dynamic Styling (In Your Error Widget Blueprint)
**Use Case:** Change appearance based on error type

**Override BP On Error Widget Initialized:**
```
Event BP On Error Widget Initialized
├─ Get Error Head → Get Text → Contains "Network"?
│  ├─ True: Set Color and Opacity (Red)
│  └─ False: Set Color and Opacity (Default)
├─ Get Error Body → Get Text → Length > 100?
│  ├─ True: Set Font Size (12)
│  └─ False: Set Font Size (14)
└─ Play Animation: FadeIn
```

---

## Integration with Existing Systems

### 12. Game Instance Error Handling
**Use Case:** Replace existing ThrowErrorPopup calls

**In Game Instance Blueprint:**
```
Function: Throw Error Popup
├─ Input: Head (String)
├─ Input: Body (String)
└─ Show Error Popup
   ├─ Error Head: Head
   ├─ Error Body: Body
   └─ World Context Object: Self
```

**Replace existing calls:**
```
Old: Throw Error Popup
New: Show Simple Error Popup (for simple errors)
     Show Network Error Popup (for network errors)
     Show Lobby Error Popup (for lobby errors)
```

---

### 13. Player Controller Error Handling
**Use Case:** Network error handling in multiplayer

**In Player Controller Blueprint:**
```
Event On Network Error
├─ Switch on String (Error Type)
│  ├─ "Connection Lost": Show Network Error Popup
│  ├─ "Server Full": Show Lobby Error Popup
│  ├─ "Kicked": Show Error Popup Advanced (with reason)
│  └─ Default: Show Simple Error Popup
```

---

## Testing and Debugging

### 14. Test Error Widget Setup
**Use Case:** Verify your error widget works

**In any Blueprint:**
```
Event Begin Play
├─ Delay (2.0 seconds)
└─ Show Simple Error Popup
   ├─ Error Message: "Test error - if you see this, your error widget is working!"
   └─ World Context Object: Self
```

### 15. Debug Error Widget Configuration
**Use Case:** Check if error widget is properly set up

**In any Blueprint:**
```
Event Begin Play
├─ Is Error Widget Configured?
├─ Branch
│  ├─ True: Print String "Error widget is configured correctly"
│  └─ False: Print String "ERROR: Error widget not configured in Project Settings!"
```

---

## Performance Tips

### 16. Efficient Error Handling
**Use Case:** Avoid creating multiple error widgets

**Good Practice:**
```
Event On Multiple Errors
├─ Is Valid (Current Error Widget)?
├─ Branch
│  ├─ True: [Don't show new error, wait for current to close]
│  └─ False: Show Error Popup
```

**Bad Practice:**
```
Event On Error
└─ Show Error Popup (creates multiple overlapping errors)
```

---

These examples cover most common error scenarios you'll encounter in game development. Copy and modify them as needed for your specific use cases!
