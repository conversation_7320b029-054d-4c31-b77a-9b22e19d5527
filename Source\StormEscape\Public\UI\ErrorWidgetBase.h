// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UI/EnhancedUIWidgetBase.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "CommonButtonBase.h"
#include "InputAction.h"
#include "ErrorWidgetBase.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnErrorClosed);

/**
 * Enhanced Error Widget with gamepad support and improved input handling
 * Now inherits from EnhancedUIWidgetBase for automatic gamepad support
 */
UCLASS()
class STORMESCAPE_API UErrorWidgetBase : public UEnhancedUIWidgetBase
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorHead;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorBody;

	// Optional button widgets for user interaction
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCommonButtonBase* ConfirmButton;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCommonButtonBase* CancelButton;

	UPROPERTY(BlueprintAssignable, Category = "ErrorWidget")
	FOnErrorClosed OnErrorClosed;

	// Input actions for gamepad support (inherited from EnhancedUIWidgetBase)
	// These are kept for backward compatibility but the base class handles input now


	// Initialize the error widget with text and optional button configuration
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void InitErrorWidget(const FString& ErrorHeadText, const FString& ErrorBodyText,
		bool bShowConfirmButton = true, bool bShowCancelButton = false,
		const FString& ConfirmText = "OK", const FString& CancelText = "Cancel");

	// Button click handlers
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void OnConfirmClicked();

	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void OnCancelClicked();

	// Override base class input handlers for error widget specific behavior
	virtual void HandleUIConfirm() override;
	virtual void HandleUICancel() override;

protected:

	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;
	virtual void NativeOnActivated() override;
	virtual void NativeOnDeactivated() override;

private:

	// Setup button bindings
	void SetupButtonBindings();

	// Close the error widget
	void CloseErrorWidget();
};
