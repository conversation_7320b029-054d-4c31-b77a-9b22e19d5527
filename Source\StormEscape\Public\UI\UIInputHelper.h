// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "InputAction.h"
#include "EnhancedInputComponent.h"
#include "UIInputHelper.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUIConfirm);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUICancel);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUINavigateUp);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUINavigateDown);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUINavigateLeft);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUINavigateRight);

/**
 * Helper class for managing common UI input actions across different widgets
 * Provides a centralized way to handle gamepad and keyboard input for UI navigation
 */
UCLASS(BlueprintType, Blueprintable)
class STORMESCAPE_API UUIInputHelper : public UObject
{
	GENERATED_BODY()

public:
	UUIInputHelper();

	// Common UI input actions
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI Input Actions")
	UInputAction* ConfirmAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI Input Actions")
	UInputAction* CancelAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI Input Actions")
	UInputAction* NavigateUpAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI Input Actions")
	UInputAction* NavigateDownAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI Input Actions")
	UInputAction* NavigateLeftAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI Input Actions")
	UInputAction* NavigateRightAction;

	// Delegates for UI events
	UPROPERTY(BlueprintAssignable, Category = "UI Events")
	FOnUIConfirm OnUIConfirm;

	UPROPERTY(BlueprintAssignable, Category = "UI Events")
	FOnUICancel OnUICancel;

	UPROPERTY(BlueprintAssignable, Category = "UI Events")
	FOnUINavigateUp OnUINavigateUp;

	UPROPERTY(BlueprintAssignable, Category = "UI Events")
	FOnUINavigateDown OnUINavigateDown;

	UPROPERTY(BlueprintAssignable, Category = "UI Events")
	FOnUINavigateLeft OnUINavigateLeft;

	UPROPERTY(BlueprintAssignable, Category = "UI Events")
	FOnUINavigateRight OnUINavigateRight;

	// Setup input bindings for a specific player controller
	UFUNCTION(BlueprintCallable, Category = "UI Input")
	void SetupInputBindings(APlayerController* PlayerController);

	// Clean up input bindings
	UFUNCTION(BlueprintCallable, Category = "UI Input")
	void CleanupInputBindings(APlayerController* PlayerController);

	// Static helper functions for common UI operations
	UFUNCTION(BlueprintCallable, Category = "UI Input", meta = (WorldContext = "WorldContextObject"))
	static UUIInputHelper* CreateUIInputHelper(UObject* WorldContextObject);

	// Get the default UI input actions from project settings
	UFUNCTION(BlueprintPure, Category = "UI Input")
	static void GetDefaultUIInputActions(UInputAction*& OutConfirmAction, UInputAction*& OutCancelAction,
		UInputAction*& OutNavigateUpAction, UInputAction*& OutNavigateDownAction,
		UInputAction*& OutNavigateLeftAction, UInputAction*& OutNavigateRightAction);

protected:
	// Input action handlers
	UFUNCTION()
	void HandleConfirmInput();

	UFUNCTION()
	void HandleCancelInput();

	UFUNCTION()
	void HandleNavigateUpInput();

	UFUNCTION()
	void HandleNavigateDownInput();

	UFUNCTION()
	void HandleNavigateLeftInput();

	UFUNCTION()
	void HandleNavigateRightInput();

private:
	// Store input binding handles for cleanup
	TArray<FInputBindingHandle> InputBindingHandles;

	// Reference to the player controller for cleanup
	TWeakObjectPtr<APlayerController> BoundPlayerController;
};
