// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/UIInputHelper.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "EnhancedInputComponent.h"

UUIInputHelper::UUIInputHelper()
{
	// Initialize with null actions - these should be set in Blueprint or through project settings
	ConfirmAction = nullptr;
	CancelAction = nullptr;
	NavigateUpAction = nullptr;
	NavigateDownAction = nullptr;
	NavigateLeftAction = nullptr;
	NavigateRightAction = nullptr;
}

void UUIInputHelper::SetupInputBindings(APlayerController* PlayerController)
{
	if (!PlayerController)
	{
		UE_LOG(LogTemp, Warning, TEXT("UIInputHelper: PlayerController is null"));
		return;
	}

	// Clean up any existing bindings first
	CleanupInputBindings(PlayerController);

	UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerController->InputComponent);
	if (!EnhancedInputComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("UIInputHelper: PlayerController does not have EnhancedInputComponent"));
		return;
	}

	BoundPlayerController = PlayerController;

	// Bind confirm action
	if (ConfirmAction)
	{
		FInputBindingHandle ConfirmHandle = EnhancedInputComponent->BindAction(
			ConfirmAction, ETriggerEvent::Started, this, &UUIInputHelper::HandleConfirmInput);
		InputBindingHandles.Add(ConfirmHandle);
	}

	// Bind cancel action
	if (CancelAction)
	{
		FInputBindingHandle CancelHandle = EnhancedInputComponent->BindAction(
			CancelAction, ETriggerEvent::Started, this, &UUIInputHelper::HandleCancelInput);
		InputBindingHandles.Add(CancelHandle);
	}

	// Bind navigation actions
	if (NavigateUpAction)
	{
		FInputBindingHandle UpHandle = EnhancedInputComponent->BindAction(
			NavigateUpAction, ETriggerEvent::Started, this, &UUIInputHelper::HandleNavigateUpInput);
		InputBindingHandles.Add(UpHandle);
	}

	if (NavigateDownAction)
	{
		FInputBindingHandle DownHandle = EnhancedInputComponent->BindAction(
			NavigateDownAction, ETriggerEvent::Started, this, &UUIInputHelper::HandleNavigateDownInput);
		InputBindingHandles.Add(DownHandle);
	}

	if (NavigateLeftAction)
	{
		FInputBindingHandle LeftHandle = EnhancedInputComponent->BindAction(
			NavigateLeftAction, ETriggerEvent::Started, this, &UUIInputHelper::HandleNavigateLeftInput);
		InputBindingHandles.Add(LeftHandle);
	}

	if (NavigateRightAction)
	{
		FInputBindingHandle RightHandle = EnhancedInputComponent->BindAction(
			NavigateRightAction, ETriggerEvent::Started, this, &UUIInputHelper::HandleNavigateRightInput);
		InputBindingHandles.Add(RightHandle);
	}

	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Input bindings setup complete with %d bindings"), InputBindingHandles.Num());
}

void UUIInputHelper::CleanupInputBindings(APlayerController* PlayerController)
{
	if (!PlayerController && BoundPlayerController.IsValid())
	{
		PlayerController = BoundPlayerController.Get();
	}

	if (PlayerController)
	{
		if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerController->InputComponent))
		{
			for (const FInputBindingHandle& Handle : InputBindingHandles)
			{
				EnhancedInputComponent->RemoveBinding(Handle);
			}
		}
	}

	InputBindingHandles.Empty();
	BoundPlayerController.Reset();

	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Input bindings cleaned up"));
}

UUIInputHelper* UUIInputHelper::CreateUIInputHelper(UObject* WorldContextObject)
{
	if (!WorldContextObject)
	{
		UE_LOG(LogTemp, Warning, TEXT("UIInputHelper: WorldContextObject is null"));
		return nullptr;
	}

	UUIInputHelper* InputHelper = NewObject<UUIInputHelper>(WorldContextObject);
	
	// Get default input actions from project settings or assign defaults
	UInputAction* ConfirmAction = nullptr;
	UInputAction* CancelAction = nullptr;
	UInputAction* NavigateUpAction = nullptr;
	UInputAction* NavigateDownAction = nullptr;
	UInputAction* NavigateLeftAction = nullptr;
	UInputAction* NavigateRightAction = nullptr;

	GetDefaultUIInputActions(ConfirmAction, CancelAction, NavigateUpAction, NavigateDownAction, NavigateLeftAction, NavigateRightAction);

	InputHelper->ConfirmAction = ConfirmAction;
	InputHelper->CancelAction = CancelAction;
	InputHelper->NavigateUpAction = NavigateUpAction;
	InputHelper->NavigateDownAction = NavigateDownAction;
	InputHelper->NavigateLeftAction = NavigateLeftAction;
	InputHelper->NavigateRightAction = NavigateRightAction;

	return InputHelper;
}

void UUIInputHelper::GetDefaultUIInputActions(UInputAction*& OutConfirmAction, UInputAction*& OutCancelAction,
	UInputAction*& OutNavigateUpAction, UInputAction*& OutNavigateDownAction,
	UInputAction*& OutNavigateLeftAction, UInputAction*& OutNavigateRightAction)
{
	// TODO: Load these from project settings or data assets
	// For now, set to nullptr - these should be configured in Blueprint
	OutConfirmAction = nullptr;
	OutCancelAction = nullptr;
	OutNavigateUpAction = nullptr;
	OutNavigateDownAction = nullptr;
	OutNavigateLeftAction = nullptr;
	OutNavigateRightAction = nullptr;

	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Default UI input actions retrieved (configure in Blueprint)"));
}

void UUIInputHelper::HandleConfirmInput()
{
	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Confirm input triggered"));
	OnUIConfirm.Broadcast();
}

void UUIInputHelper::HandleCancelInput()
{
	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Cancel input triggered"));
	OnUICancel.Broadcast();
}

void UUIInputHelper::HandleNavigateUpInput()
{
	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Navigate up input triggered"));
	OnUINavigateUp.Broadcast();
}

void UUIInputHelper::HandleNavigateDownInput()
{
	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Navigate down input triggered"));
	OnUINavigateDown.Broadcast();
}

void UUIInputHelper::HandleNavigateLeftInput()
{
	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Navigate left input triggered"));
	OnUINavigateLeft.Broadcast();
}

void UUIInputHelper::HandleNavigateRightInput()
{
	UE_LOG(LogTemp, Log, TEXT("UIInputHelper: Navigate right input triggered"));
	OnUINavigateRight.Broadcast();
}
