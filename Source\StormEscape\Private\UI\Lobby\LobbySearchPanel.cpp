﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Lobby/LobbySearchPanel.h"
#include "Components/WidgetSwitcher.h"
#include "Core/StormEscapeGameInstance.h"
#include "UI/Lobby/LobbySearchList.h"
#include "UI/Lobby/LobbySearchPage.h"


void ULobbySearchPanel::NativeConstruct()
{
	Super::NativeConstruct();
	GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(this);
	if (GameInstance && !GameInstance->OnStormSessionsFound.IsAlreadyBound(this, &ULobbySearchPanel::OnLobbiesResultsReceived))
	{
		GameInstance->OnStormSessionsFound.AddDynamic(this, &ULobbySearchPanel::OnLobbiesResultsReceived);
	}
}

void ULobbySearchPanel::GenerateTestLobbies(int32 NumLobbies)
{
	TArray<FLobbySearchResult> TestLobbies;
	TestLobbies.Reserve(NumLobbies);

	for (int32 i = 0; i < NumLobbies; ++i)
	{
		FLobbySearchResult LobbyData;
		LobbyData.LobbyInfo.LobbyName = FString::Printf(TEXT("Lobby_%s"), *FGuid::NewGuid().ToString(EGuidFormats::Digits).Left(6));
		LobbyData.LobbyInfo.MaxPlayers = FMath::RandRange(2, 16);
		LobbyData.LobbyInfo.CurrentPlayers = FMath::RandRange(1, LobbyData.LobbyInfo.MaxPlayers);
		LobbyData.Ping = FMath::RandRange(10, 250);
		TestLobbies.Add(MoveTemp(LobbyData));
	}

	OnLobbiesResultsReceived(TestLobbies);
}

void ULobbySearchPanel::OnLobbiesResultsReceived(const TArray<FLobbySearchResult>& Results)
{
	AllLobbies = Results;
	ApplyAllFiltersAndDisplay();
}

void ULobbySearchPanel::SetSearchText(const FString& NewSearchText)
{
	SearchText = NewSearchText;
	ApplyAllFiltersAndDisplay();
}

void ULobbySearchPanel::SetSearchFilter(const FSearchFilter& NewFilter)
{
	SearchFilter = NewFilter;
	ApplyAllFiltersAndDisplay();
}

void ULobbySearchPanel::ApplyAllFiltersAndDisplay()
{
	FilterLobbiesBySettings();
	FilterLobbiesByName();
	SortLobbiesByPingRanges(DisplayedLobbies);
	DisplayLobbiesInternal(DisplayedLobbies);
}

void ULobbySearchPanel::FilterLobbiesBySettings()
{
	FilteredLobbies.Empty();
	for (const FLobbySearchResult& Lobby : AllLobbies)
	{
		if (PassesAllFilters(Lobby))
		{
			FilteredLobbies.Add(Lobby);
		}
	}
}

void ULobbySearchPanel::FilterLobbiesByName()
{
	DisplayedLobbies.Empty();
	for (const FLobbySearchResult& Lobby : FilteredLobbies)
	{
		const bool bMatchesName = SearchText.IsEmpty() || Lobby.LobbyInfo.LobbyName.Contains(SearchText, ESearchCase::IgnoreCase);
		if (bMatchesName)
		{
			DisplayedLobbies.Add(Lobby);
		}
	}
}

void ULobbySearchPanel::DisplayLobbies(const TArray<FLobbySearchResult>& Lobbies)
{
	DisplayLobbiesInternal(Lobbies);
}

void ULobbySearchPanel::DisplayLobbiesInternal(const TArray<FLobbySearchResult>& Lobbies)
{
	const int32 PageCount = FMath::CeilToInt(static_cast<float>(Lobbies.Num()) / static_cast<float>(ResultsPerPage));
	OnPagesRefreshed(PageCount);

	PageSwitcher->ClearChildren();

	for (int32 PageIndex = 0; PageIndex < PageCount; ++PageIndex)
	{
		if (PageClass == nullptr)
		{
			UE_LOG(LogTemp, Error, TEXT("ULobbySearchPage::CreateWidget: Page Class Is Invalid"));
			return;
		}

		ULobbySearchPage* NewPage = CreateWidget<ULobbySearchPage>(this, PageClass);
		if (!IsValid(NewPage))
		{
			UE_LOG(LogTemp, Error, TEXT("ULobbySearchPage::CreateWidget: Page Class Is Invalid"));
			return;
		}

		const int32 StartIndex = PageIndex * ResultsPerPage;
		const int32 EndIndex = FMath::Min(StartIndex + ResultsPerPage, Lobbies.Num());

		TArray<FLobbySearchResult> PageResults;
		for (int32 i = StartIndex; i < EndIndex; ++i)
		{
			PageResults.Add(Lobbies[i]);
		}

		const int32 Half = PageResults.Num() / 2;
		TArray<FLobbySearchResult> LeftResults;
		TArray<FLobbySearchResult> RightResults;

		for (int32 i = 0; i < PageResults.Num(); ++i)
		{
			if (i < ResultsPerPage / 2)
			{
				LeftResults.Add(PageResults[i]);
			}
			else
			{
				RightResults.Add(PageResults[i]);
			}
		}

		if (NewPage->LeftLobbySearchList)
		{
			NewPage->LeftLobbySearchList->PopulateList(LeftResults);
		}
		if (NewPage->RightLobbySearchList)
		{
			NewPage->RightLobbySearchList->PopulateList(RightResults);
		}

		PageSwitcher->AddChild(NewPage);
	}
}

bool ULobbySearchPanel::PassesPingFilter(const FLobbySearchResult& Lobby) const
{
	// 0 means no ping filter
	return (SearchFilter.MaxPing == 0) || (Lobby.Ping <= SearchFilter.MaxPing);
}

bool ULobbySearchPanel::PassesMaxPlayersFilter(const FLobbySearchResult& Lobby) const
{
	return Lobby.LobbyInfo.MaxPlayers <= SearchFilter.MaxNumPlayers;
}

bool ULobbySearchPanel::PassesAllFilters(const FLobbySearchResult& Lobby) const
{
	return PassesPingFilter(Lobby) && PassesMaxPlayersFilter(Lobby);
}

void ULobbySearchPanel::SortLobbiesByPingRanges(TArray<FLobbySearchResult>& Lobbies) const
{
	TArray<FLobbySearchResult> Good, Average, Bad;

	for (const FLobbySearchResult& Lobby : Lobbies)
	{
		if (Lobby.Ping <= MaxGoodPingLimit)
		{
			Good.Add(Lobby);
		}
		else if (Lobby.Ping <= MaxAveragePingLimit)
		{
			Average.Add(Lobby);
		}
		else
		{
			Bad.Add(Lobby);
		}
	}

	Lobbies.Reset();
	Lobbies.Append(Good);
	Lobbies.Append(Average);
	Lobbies.Append(Bad);
}
