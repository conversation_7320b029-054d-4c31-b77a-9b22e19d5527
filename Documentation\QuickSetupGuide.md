# Quick Setup Guide - Gamepad Input Mapping

## Step-by-Step Setup

### 1. Open StormEscapePlayerController Blueprint
- Navigate to your StormEscapePlayerController Blueprint
- If you don't have one, create a Blueprint based on the C++ StormEscapePlayerController class

### 2. Configure Input Mapping Properties
In the **Details** panel, find the **Input** section and set:

**UI Controls Input Mapping:**
```
/Game/StormEscape/Input/Mappings/IM_UIControls
```

**Game Controls Input Mapping:**
```
/Game/StormEscape/Input/IMC_FirstPersonMappingContext
```

### 3. Verify Input Mapping Assets

#### Check IM_UIControls.uasset
- Location: `Content/StormEscape/Input/Mappings/IM_UIControls.uasset`
- Should contain UI navigation bindings for gamepad
- Example bindings:
  - Navigate: Gamepad Left Stick
  - Confirm: Gamepad Face Button Bottom
  - Cancel: Gamepad Face Button Right

#### Check IMC_FirstPersonMappingContext.uasset  
- Location: `Content/StormEscape/Input/IMC_FirstPersonMappingContext.uasset`
- Should contain gameplay bindings for gamepad
- Example bindings:
  - Move: Gamepad Left Stick
  - Look: Gamepad Right Stick
  - Jump: Gamepad Face Button Bottom

### 4. Test the System

1. **Test UI Navigation:**
   - Open any menu (pause menu, settings, etc.)
   - Verify gamepad can navigate UI elements
   - Check that UI input mapping is active

2. **Test Gameplay:**
   - Return to gameplay mode
   - Verify gamepad controls work for character movement
   - Check that game input mapping is active

### 5. Common UI Input Actions for Gamepad

Make sure your UI widgets are set up to handle these common gamepad inputs:

```
Navigate Up/Down/Left/Right - Gamepad D-Pad or Left Stick
Confirm/Select - Gamepad Face Button Bottom (A on Xbox, X on PlayStation)
Cancel/Back - Gamepad Face Button Right (B on Xbox, Circle on PlayStation)
Tab Previous - Gamepad Left Shoulder Button
Tab Next - Gamepad Right Shoulder Button
```

## Troubleshooting

### Issue: Gamepad doesn't work in UI
**Solution:** 
- Check that IM_UIControls is properly assigned
- Verify CommonUI gamepad support is enabled
- Ensure UI widgets have proper focus handling

### Issue: Gamepad doesn't work in game
**Solution:**
- Check that IMC_FirstPersonMappingContext is properly assigned
- Verify the character's input mapping is not conflicting
- Check Enhanced Input subsystem is working

### Issue: Input conflicts between UI and game
**Solution:**
- Use different input actions for UI vs gameplay
- Verify priority settings (UI should be higher priority)
- Check for duplicate bindings

## Verification Checklist

- [ ] StormEscapePlayerController has both input mapping contexts assigned
- [ ] IM_UIControls contains gamepad UI navigation bindings
- [ ] IMC_FirstPersonMappingContext contains gamepad gameplay bindings  
- [ ] UI menus respond to gamepad navigation
- [ ] Gameplay controls work with gamepad
- [ ] Switching between UI and game modes works correctly
- [ ] No input conflicts between UI and game modes

## Next Steps

Once basic setup is complete:
1. Fine-tune gamepad sensitivity settings
2. Add haptic feedback for UI interactions
3. Customize button prompts for different controller types
4. Test with different gamepad types (Xbox, PlayStation, etc.)
