# Enhanced UI System Guide

This document covers the comprehensive improvements made to the UI system in StormEscape, including enhanced error widgets, gamepad support, and the new UI framework.

## Overview of Improvements

### 1. Enhanced Error Widget System
- **Improved ErrorWidgetBase** with full gamepad support
- **Button configuration** (confirm/cancel buttons)
- **Automatic input handling** through inheritance
- **Better focus management**
- **Enhanced UIHelperLibrary** with advanced popup options

### 2. New UI Framework Components
- **EnhancedUIWidgetBase** - Base class for all UI widgets
- **UIInputHelper** - Centralized input management
- **Automatic gamepad support** for all UI widgets
- **Input mapping context integration**

### 3. Gamepad Input Mapping System
- **Automatic context switching** between UI and game modes
- **Priority-based input handling**
- **Seamless integration** with existing UIManager

## New Classes and Components

### EnhancedUIWidgetBase
Base class that all UI widgets should inherit from to get automatic gamepad support.

**Features:**
- Automatic input helper setup
- Focus management
- Gamepad navigation support
- Blueprint events for UI input

**Usage:**
```cpp
// C++ Usage
class STORMESCAPE_API UMyCustomWidget : public UEnhancedUIWidgetBase
{
    // Override input handlers
    virtual void HandleUIConfirm() override;
    virtual void HandleUICancel() override;
};
```

### UIInputHelper
Centralized input management for UI widgets.

**Features:**
- Common UI input actions (confirm, cancel, navigate)
- Automatic binding/unbinding
- Event delegates for input handling
- Static helper functions

### Enhanced ErrorWidgetBase
Improved error widget with full gamepad support.

**New Features:**
- Optional confirm/cancel buttons
- Automatic focus management
- Gamepad input support
- Customizable button text
- Better error handling

## Usage Examples

### 1. Creating Enhanced Error Popups

**Simple Error Popup:**
```cpp
// C++ - Simple error with OK button
UUIHelperLibrary::ShowErrorPopup("Error", "Something went wrong!", this);

// C++ - Advanced error with custom buttons
UUIHelperLibrary::ShowErrorPopupAdvanced(
    "Confirmation", 
    "Are you sure you want to quit?", 
    this, 
    true,  // Show confirm button
    true,  // Show cancel button
    "Yes", // Confirm text
    "No"   // Cancel text
);

// C++ - Confirmation dialog
UUIHelperLibrary::ShowConfirmationDialog(
    "Delete Save", 
    "This action cannot be undone.", 
    this
);
```

**Blueprint Usage:**
- Use "Show Error Popup Advanced" node
- Configure button visibility and text
- Handle OnErrorClosed event if needed

### 2. Creating Custom UI Widgets

**Inherit from EnhancedUIWidgetBase:**
```cpp
UCLASS()
class UMyMenuWidget : public UEnhancedUIWidgetBase
{
    GENERATED_BODY()

public:
    // Override input handlers
    virtual void HandleUIConfirm() override
    {
        // Handle confirm input (e.g., select current option)
        Super::HandleUIConfirm();
    }

    virtual void HandleUICancel() override
    {
        // Handle cancel input (e.g., close menu)
        CloseMenu();
        Super::HandleUICancel();
    }

    virtual void HandleUINavigateUp() override
    {
        // Handle up navigation
        NavigateToNextOption(-1);
        Super::HandleUINavigateUp();
    }
};
```

**Blueprint Implementation:**
1. Create widget Blueprint based on EnhancedUIWidgetBase
2. Override "On UI Confirm Pressed", "On UI Cancel Pressed" events
3. Set "Initial Focus Widget" for automatic focus
4. Enable/disable "Use Automatic Input Helper" as needed

### 3. Manual Input Helper Usage

**For widgets that need custom input handling:**
```cpp
void UMyWidget::NativeConstruct()
{
    Super::NativeConstruct();
    
    // Create custom input helper
    UIInputHelper = UUIInputHelper::CreateUIInputHelper(this);
    
    // Bind to specific events
    UIInputHelper->OnUIConfirm.AddDynamic(this, &UMyWidget::OnConfirmPressed);
    UIInputHelper->OnUICancel.AddDynamic(this, &UMyWidget::OnCancelPressed);
    
    // Setup input bindings
    UIInputHelper->SetupInputBindings(GetOwningPlayer());
}
```

## Configuration

### 1. Input Mapping Context Setup

Ensure your StormEscapePlayerController has the input mapping contexts configured:
- **UI Controls Input Mapping**: `/Game/StormEscape/Input/Mappings/IM_UIControls`
- **Game Controls Input Mapping**: `/Game/StormEscape/Input/IMC_FirstPersonMappingContext`

### 2. Error Widget Class Configuration

In Project Settings > Custom UI Settings:
- Set **Error Widget Class** to your enhanced error widget Blueprint

### 3. Input Actions Configuration

Create or configure these input actions in your UI input mapping context:
- **UI_Confirm** - Gamepad Face Button Bottom (A/X)
- **UI_Cancel** - Gamepad Face Button Right (B/Circle)
- **UI_Navigate** - Gamepad D-Pad or Left Stick
- **UI_TabLeft** - Gamepad Left Shoulder
- **UI_TabRight** - Gamepad Right Shoulder

## Best Practices

### 1. Widget Inheritance
- Always inherit from `EnhancedUIWidgetBase` for new UI widgets
- Use automatic input helper unless you need custom behavior
- Override input handlers in C++ or Blueprint events

### 2. Focus Management
- Set `InitialFocusWidget` for proper gamepad navigation
- Use `SetFocusToWidget()` to manually control focus
- Test focus flow with gamepad navigation

### 3. Error Handling
- Use `ShowErrorPopupAdvanced()` for better control
- Provide clear, actionable error messages
- Use confirmation dialogs for destructive actions

### 4. Input Actions
- Keep UI input actions separate from game input actions
- Use consistent button mappings across all UI
- Test with different controller types

## Migration Guide

### Existing Error Widgets
1. Change base class from `UCommonActivatableWidget` to `UEnhancedUIWidgetBase`
2. Remove manual input mode management
3. Use new `InitErrorWidget()` parameters for button configuration
4. Test gamepad functionality

### Existing UI Widgets
1. Consider migrating to `EnhancedUIWidgetBase`
2. Remove manual input binding code
3. Override virtual input handlers instead
4. Set `InitialFocusWidget` for focus management

### Input Mapping
1. Ensure UI input mapping context is configured
2. Test automatic context switching
3. Verify gamepad bindings work in both UI and game modes

## Troubleshooting

### Common Issues

**Gamepad not working in UI:**
- Check UI input mapping context assignment
- Verify input actions are properly configured
- Ensure UIManager is properly initialized

**Focus not working:**
- Set `InitialFocusWidget` property
- Check widget visibility and focusability
- Verify CommonUI focus system is working

**Input conflicts:**
- Check input action priorities
- Ensure UI and game contexts don't conflict
- Verify input mapping context switching

**Error widget not showing:**
- Check CustomUISettings configuration
- Verify ErrorWidgetClass is set
- Check UIManager initialization

### Debug Logging

Enable these log categories for debugging:
- `LogTemp` - General UI system logs
- Look for messages containing:
  - "UIManager"
  - "Error Widget"
  - "Input Mapping Context"
  - "EnhancedUIWidgetBase"
