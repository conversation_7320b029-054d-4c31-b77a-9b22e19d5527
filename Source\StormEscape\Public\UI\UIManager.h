#pragma once

#include "CoreMinimal.h"
#include "UIManager.generated.h"

class UBaseMainLayerUI;
class UCommonActivatableWidget;
class UCommonActivatableWidgetStack;

// Represents the different UI layers where widgets can be added.
// Used by the UIManager to route widgets to the correct widget stack.
UENUM(BlueprintType)
enum class EWidgetLayer : uint8
{
	// In-game UI elements that are non-intrusive and part of regular gameplay (e.g., health bars, minimap, ammo counter).
	GameUI UMETA(DisplayName = "Game UI"),

	// Menus that interrupt gameplay and may require exclusive input (e.g., pause menu, settings, main menu).
	Menu UMETA(DisplayName = "Menu"),

	// Non-blocking transient popups, such as notifications, alerts, or toasts. Input is not blocked behind them.
	Popup UMETA(DisplayName = "Popup"),

	// Blocking modal windows requiring player interaction (e.g., confirmation dialogs, error popups).
	Modal UMETA(DisplayName = "Modal")
};

UENUM(BlueprintType)
enum class EInputModeType : uint8
{
	UIOnly,
	GameOnly,
	GameAndUI
};


UCLASS()
class STORMESCAPE_API UUIManager : public UObject
{
	GENERATED_BODY()

public:

	/** Called internally at BeginPlay in PlayerController */
	static void Initialize(APlayerController* PlayerController, UBaseMainLayerUI* MainLayerUIInstance);

	/** Adds widget to the stack, sets input mode and cursor, and manages input mapping contexts for gamepad support */
	UFUNCTION(BlueprintCallable, Category = "UIManager" , meta = (WorldContext = "WorldContextObject"))
		static UCommonActivatableWidget* AddWidgetToStack(UObject* WorldContextObject,
		TSubclassOf<UCommonActivatableWidget> WidgetClass,
		EWidgetLayer TargetLayer,
		EInputModeType InputMode = EInputModeType::UIOnly,
		bool bShowMouseCursor = true,
		bool bClearCurrentStack = false);

	/** An easier Way to Set the player input.
	 basically it just calls the SetInputMode and MouseCursor on the local player controller and manages input mapping contexts for gamepad support */
	UFUNCTION(BlueprintCallable, Category = "UIManager" , meta = (WorldContext = "WorldContextObject"))
	static void SetInputMode(UObject* WorldContextObject,
		EInputModeType InputMode = EInputModeType::UIOnly,
		bool bShowMouseCursor = true, UUserWidget* Widget = nullptr);


	/* Returns the widget containing the layers stacks (MainBaseLayerUI) */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	UBaseMainLayerUI* GetMainLayerUI() const
	{
		return MainLayerUI.Get();
	}

private:
	/** Helper function to manage input mapping contexts based on input mode */
	static void ManageInputMappingContexts(APlayerController* PlayerController, EInputModeType InputMode);
	
private:

	static TWeakObjectPtr<UBaseMainLayerUI> MainLayerUI;
	static TWeakObjectPtr<APlayerController> CachedPC;

	static UCommonActivatableWidgetStack* GetTargetStack(EWidgetLayer Layer);
};
