# CommonUI-Based Enhanced UI System Guide

This document covers the proper CommonUI-based approach to gamepad support and enhanced UI widgets in StormEscape, leveraging Unreal's native CommonUI system.

## Overview of Improvements

### 1. Enhanced Error Widget System
- **Improved ErrorWidgetBase** using CommonUI's native gamepad support
- **Button configuration** (confirm/cancel buttons)
- **CommonUI input handling** through native back action support
- **Native focus management** using CommonUI's focus system
- **Enhanced UIHelperLibrary** with advanced popup options

### 2. CommonUI Framework Integration
- **CommonUIWidgetBase** - Base class leveraging CommonUI features
- **Native CommonUI input handling** - No custom input systems needed
- **Automatic gamepad support** through CommonUI configuration
- **Input mapping context integration** with Enhanced Input

### 3. Gamepad Input Mapping System
- **Automatic context switching** between UI and game modes
- **Priority-based input handling** using Enhanced Input
- **CommonUI integration** with existing UIManager

## New Classes and Components

### CommonUIWidgetBase
Base class that leverages CommonUI's native gamepad support and focus management.

**Features:**
- Native CommonUI input handling
- Automatic focus management
- Back action support
- Blueprint events for common actions
- Focus restoration

**Usage:**
```cpp
// C++ Usage
class STORMESCAPE_API UMyCustomWidget : public UCommonUIWidgetBase
{
    // Override CommonUI methods
    virtual bool NativeOnHandleBackAction() override;
    virtual UWidget* NativeGetDesiredFocusTarget() const override;
};
```

### Enhanced ErrorWidgetBase
Improved error widget using CommonUI's native systems.

**CommonUI Features:**
- Native back action handling
- Automatic focus management through NativeGetDesiredFocusTarget()
- CommonUI input routing
- Blueprint implementable events
- Proper widget activation/deactivation

**New Features:**
- Optional confirm/cancel buttons
- Customizable button text
- Better error handling
- CommonUI-compliant focus behavior

## Usage Examples

### 1. Creating Enhanced Error Popups

**Simple Error Popup:**
```cpp
// C++ - Simple error with OK button
UUIHelperLibrary::ShowErrorPopup("Error", "Something went wrong!", this);

// C++ - Advanced error with custom buttons
UUIHelperLibrary::ShowErrorPopupAdvanced(
    "Confirmation", 
    "Are you sure you want to quit?", 
    this, 
    true,  // Show confirm button
    true,  // Show cancel button
    "Yes", // Confirm text
    "No"   // Cancel text
);

// C++ - Confirmation dialog
UUIHelperLibrary::ShowConfirmationDialog(
    "Delete Save", 
    "This action cannot be undone.", 
    this
);
```

**Blueprint Usage:**
- Use "Show Error Popup Advanced" node
- Configure button visibility and text
- Handle OnErrorClosed event if needed

### 2. Creating Custom UI Widgets with CommonUI

**Inherit from CommonUIWidgetBase:**
```cpp
UCLASS()
class UMyMenuWidget : public UCommonUIWidgetBase
{
    GENERATED_BODY()

public:
    // Override CommonUI back action
    virtual bool NativeOnHandleBackAction() override
    {
        // Handle back input (e.g., close menu)
        CloseMenu();
        return true; // We handled the action
    }

    // Override focus target
    virtual UWidget* NativeGetDesiredFocusTarget() const override
    {
        if (FirstMenuButton && FirstMenuButton->GetVisibility() == ESlateVisibility::Visible)
        {
            return FirstMenuButton;
        }
        return Super::NativeGetDesiredFocusTarget();
    }

private:
    UPROPERTY(meta = (BindWidget))
    UCommonButtonBase* FirstMenuButton;
};
```

**Blueprint Implementation:**
1. Create widget Blueprint based on CommonUIWidgetBase
2. Override "BP On Handle Back Action" event for custom back behavior
3. Set "Desired Focus Target" widget for automatic focus
4. Use "BP On Set Initial Focus" event for complex focus logic
5. CommonUI handles all gamepad navigation automatically

### 3. CommonUI Configuration

**Your project already has CommonUI configured:**
- CommonInput settings in DefaultGame.ini
- Input data asset: `/Game/StormEscape/Input/Data/CUI_InputData`
- Platform-specific controller data configured
- Gamepad support enabled

## Configuration

### 1. Input Mapping Context Setup

Ensure your StormEscapePlayerController has the input mapping contexts configured:
- **UI Controls Input Mapping**: `/Game/StormEscape/Input/Mappings/IM_UIControls`
- **Game Controls Input Mapping**: `/Game/StormEscape/Input/IMC_FirstPersonMappingContext`

### 2. Error Widget Class Configuration

In Project Settings > Custom UI Settings:
- Set **Error Widget Class** to your enhanced error widget Blueprint

### 3. Input Actions Configuration

Create or configure these input actions in your UI input mapping context:
- **UI_Confirm** - Gamepad Face Button Bottom (A/X)
- **UI_Cancel** - Gamepad Face Button Right (B/Circle)
- **UI_Navigate** - Gamepad D-Pad or Left Stick
- **UI_TabLeft** - Gamepad Left Shoulder
- **UI_TabRight** - Gamepad Right Shoulder

## Best Practices

### 1. Widget Inheritance
- Always inherit from `EnhancedUIWidgetBase` for new UI widgets
- Use automatic input helper unless you need custom behavior
- Override input handlers in C++ or Blueprint events

### 2. Focus Management
- Set `InitialFocusWidget` for proper gamepad navigation
- Use `SetFocusToWidget()` to manually control focus
- Test focus flow with gamepad navigation

### 3. Error Handling
- Use `ShowErrorPopupAdvanced()` for better control
- Provide clear, actionable error messages
- Use confirmation dialogs for destructive actions

### 4. Input Actions
- Keep UI input actions separate from game input actions
- Use consistent button mappings across all UI
- Test with different controller types

## Migration Guide

### Existing Error Widgets
1. Change base class from `UCommonActivatableWidget` to `UEnhancedUIWidgetBase`
2. Remove manual input mode management
3. Use new `InitErrorWidget()` parameters for button configuration
4. Test gamepad functionality

### Existing UI Widgets
1. Consider migrating to `EnhancedUIWidgetBase`
2. Remove manual input binding code
3. Override virtual input handlers instead
4. Set `InitialFocusWidget` for focus management

### Input Mapping
1. Ensure UI input mapping context is configured
2. Test automatic context switching
3. Verify gamepad bindings work in both UI and game modes

## Troubleshooting

### Common Issues

**Gamepad not working in UI:**
- Check UI input mapping context assignment
- Verify input actions are properly configured
- Ensure UIManager is properly initialized

**Focus not working:**
- Set `InitialFocusWidget` property
- Check widget visibility and focusability
- Verify CommonUI focus system is working

**Input conflicts:**
- Check input action priorities
- Ensure UI and game contexts don't conflict
- Verify input mapping context switching

**Error widget not showing:**
- Check CustomUISettings configuration
- Verify ErrorWidgetClass is set
- Check UIManager initialization

### Debug Logging

Enable these log categories for debugging:
- `LogTemp` - General UI system logs
- Look for messages containing:
  - "UIManager"
  - "Error Widget"
  - "Input Mapping Context"
  - "EnhancedUIWidgetBase"
