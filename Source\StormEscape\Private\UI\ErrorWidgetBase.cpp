// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/ErrorWidgetBase.h"
#include "Components/TextBlock.h"
#include "CommonButtonBase.h"
#include "UI/UIManager.h"

void UErrorWidgetBase::InitErrorWidget(const FString& ErrorHeadText, const FString& ErrorBodyText,
	bool bShowConfirmButton, bool bShowCancelButton, const FString& ConfirmText, const FString& CancelText)
{
	// Set error text
	if (ErrorHead)
	{
		ErrorHead->SetText(FText::FromString(ErrorHeadText));
	}

	if (ErrorBody)
	{
		ErrorBody->SetText(FText::FromString(ErrorBodyText));
	}

	// Configure buttons
	if (ConfirmButton)
	{
		ConfirmButton->SetVisibility(bShowConfirmButton ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (bShowConfirmButton)
		{
			ConfirmButton->SetButtonText(FText::FromString(ConfirmText));
		}
	}

	if (CancelButton)
	{
		CancelButton->SetVisibility(bShowCancelButton ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (bShowCancelButton)
		{
			CancelButton->SetButtonText(FText::FromString(CancelText));
		}
	}

	// Call Blueprint event
	BP_OnErrorWidgetInitialized();

	UE_LOG(LogTemp, Log, TEXT("Error Widget: Initialized with Head='%s', Body='%s'"), *ErrorHeadText, *ErrorBodyText);
}

void UErrorWidgetBase::InitErrorWidgetSimple(const FString& ErrorHeadText, const FString& ErrorBodyText)
{
	// Use default settings from Blueprint properties
	InitErrorWidget(
		ErrorHeadText,
		ErrorBodyText,
		bShowConfirmButtonByDefault,
		bShowCancelButtonByDefault,
		DefaultConfirmText.ToString(),
		DefaultCancelText.ToString()
	);
}

void UErrorWidgetBase::OnConfirmClicked()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Confirm button clicked"));

	// Broadcast event for Blueprint binding
	OnErrorConfirm.Broadcast();

	// Call Blueprint implementable event
	BP_OnConfirmClicked();

	// Close widget if configured to do so
	if (bCloseOnConfirm)
	{
		CloseErrorWidget();
	}
}

void UErrorWidgetBase::OnCancelClicked()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Cancel button clicked"));

	// Broadcast event for Blueprint binding
	OnErrorCancel.Broadcast();

	// Call Blueprint implementable event
	BP_OnCancelClicked();

	// Close widget if configured to do so
	if (bCloseOnCancel)
	{
		CloseErrorWidget();
	}
}

void UErrorWidgetBase::SetInitialFocus()
{
	// Set focus to the confirm button if available, otherwise cancel button
	if (ConfirmButton && ConfirmButton->GetVisibility() == ESlateVisibility::Visible)
	{
		ConfirmButton->SetFocus();
		UE_LOG(LogTemp, Log, TEXT("Error Widget: Focus set to confirm button"));
	}
	else if (CancelButton && CancelButton->GetVisibility() == ESlateVisibility::Visible)
	{
		CancelButton->SetFocus();
		UE_LOG(LogTemp, Log, TEXT("Error Widget: Focus set to cancel button"));
	}
}

void UErrorWidgetBase::NativeConstruct()
{
	Super::NativeConstruct();

	UE_LOG(LogTemp, Log, TEXT("Error Widget: Constructing"));

	SetupButtonBindings();
}

void UErrorWidgetBase::NativeDestruct()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Destructing"));

	OnErrorClosed.Broadcast();

	Super::NativeDestruct();
}

void UErrorWidgetBase::NativeOnActivated()
{
	Super::NativeOnActivated();
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Activated"));

	// Set initial focus when the widget is activated
	SetInitialFocus();

	// Call Blueprint event
	BP_OnErrorWidgetActivated();
}

void UErrorWidgetBase::NativeOnDeactivated()
{
	Super::NativeOnDeactivated();
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Deactivated"));

	// Call Blueprint event
	BP_OnErrorWidgetDeactivated();
}

bool UErrorWidgetBase::NativeOnHandleBackAction()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Back action triggered"));

	// Call the parent implementation which handles BP_OnHandleBackAction
	if (Super::NativeOnHandleBackAction())
	{
		return true; // Parent/Blueprint handled it
	}

	// Default behavior: trigger cancel if available, otherwise confirm
	if (CancelButton && CancelButton->GetVisibility() == ESlateVisibility::Visible)
	{
		OnCancelClicked();
	}
	else if (ConfirmButton && ConfirmButton->GetVisibility() == ESlateVisibility::Visible)
	{
		OnConfirmClicked();
	}
	else
	{
		CloseErrorWidget();
	}

	return true; // We handled the back action
}

UWidget* UErrorWidgetBase::NativeGetDesiredFocusTarget() const
{
	// Return the desired focus target for CommonUI
	if (ConfirmButton && ConfirmButton->GetVisibility() == ESlateVisibility::Visible)
	{
		return ConfirmButton;
	}
	else if (CancelButton && CancelButton->GetVisibility() == ESlateVisibility::Visible)
	{
		return CancelButton;
	}

	return Super::NativeGetDesiredFocusTarget();
}

void UErrorWidgetBase::SetupButtonBindings()
{
	// Bind button click events
	if (ConfirmButton)
	{
		ConfirmButton->OnClicked().AddUObject(this, &UErrorWidgetBase::OnConfirmClicked);
	}

	if (CancelButton)
	{
		CancelButton->OnClicked().AddUObject(this, &UErrorWidgetBase::OnCancelClicked);
	}
}

void UErrorWidgetBase::CloseErrorWidget()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Closing"));

	// Use the CommonActivatableWidget's deactivation system
	DeactivateWidget();
}
