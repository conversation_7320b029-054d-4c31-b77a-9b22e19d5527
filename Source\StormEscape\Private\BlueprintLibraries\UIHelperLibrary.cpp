// Fill out your copyright notice in the Description page of Project Settings.


#include "BlueprintLibraries/UIHelperLibrary.h"
#include "CoreMinimal.h"
#include "AdvancedSteamFriendsLibrary.h"
#include "Settings/CustomUISettings.h"
#include "UI/BaseMainLayerUI.h"
#include "UI/ErrorWidgetBase.h"
#include "UI/UIManager.h"
#include "Core/StormEscapeGameInstance.h"
#include "Kismet/GameplayStatics.h"


FString UUIHelperLibrary::GetProjectVersion()
{
	FString ProjectVersion;
	GConfig->GetString(
		TEXT("/Script/EngineSettings.GeneralProjectSettings"),
		TEXT("ProjectVersion"),
		ProjectVersion,
		GGameIni
	);

	return ProjectVersion;
}

bool UUIHelperLibrary::MatchesRegex(const FString& Input, const FString& Pattern)
{
	const FRegexPattern RegexPattern(Pattern);
	FRegexMatcher Matcher(RegexPattern, Input);
	return Matcher.FindNext(); 
}

void UUIHelperLibrary::ShowErrorPopup(const FString& ErrorHead, const FString& ErrorBody, UObject* WorldContextObject)
{
	// Use the advanced version with default settings
	ShowErrorPopupAdvanced(ErrorHead, ErrorBody, WorldContextObject, true, false, "OK", "Cancel");
}

void UUIHelperLibrary::ShowErrorPopupAdvanced(const FString& ErrorHead, const FString& ErrorBody,
	UObject* WorldContextObject, bool bShowConfirmButton, bool bShowCancelButton,
	const FString& ConfirmText, const FString& CancelText)
{
	if (!WorldContextObject)
	{
		UE_LOG(LogTemp, Warning, TEXT("UIHelperLibrary: WorldContextObject is null"));
		return;
	}

	// Access the error widget class from the settings
	if (UCustomUISettings* CustomUISettings = GetMutableDefault<UCustomUISettings>())
	{
		TSubclassOf<UErrorWidgetBase> ErrorWidgetClass = CustomUISettings->ErrorWidgetClass;
		if (!ErrorWidgetClass)
		{
			UE_LOG(LogTemp, Warning, TEXT("UIHelperLibrary: ErrorWidgetClass is not set in CustomUISettings"));
			return;
		}

		// Use the UIManager to spawn the widget
		UCommonActivatableWidget* SpawnedWidget = UUIManager::AddWidgetToStack(
			WorldContextObject,
			ErrorWidgetClass,
			EWidgetLayer::Modal,
			EInputModeType::UIOnly,
			true,
			false
		);

		// Initialize the error message with advanced options
		if (UErrorWidgetBase* ErrorWidget = Cast<UErrorWidgetBase>(SpawnedWidget))
		{
			ErrorWidget->InitErrorWidget(ErrorHead, ErrorBody, bShowConfirmButton, bShowCancelButton, ConfirmText, CancelText);
			UE_LOG(LogTemp, Log, TEXT("UIHelperLibrary: Error popup created successfully"));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("UIHelperLibrary: Failed to cast spawned widget to UErrorWidgetBase"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UIHelperLibrary: Failed to get CustomUISettings"));
	}
}

void UUIHelperLibrary::ShowConfirmationDialog(const FString& Title, const FString& Message,
	UObject* WorldContextObject, const FString& ConfirmText, const FString& CancelText)
{
	// Show confirmation dialog with both buttons
	ShowErrorPopupAdvanced(Title, Message, WorldContextObject, true, true, ConfirmText, CancelText);
}

void UUIHelperLibrary::ShowSimpleErrorPopup(const FString& ErrorMessage, UObject* WorldContextObject)
{
	ShowErrorPopup("Error", ErrorMessage, WorldContextObject);
}

void UUIHelperLibrary::ShowNetworkErrorPopup(const FString& ErrorMessage, UObject* WorldContextObject)
{
	ShowErrorPopup("Network Error", ErrorMessage, WorldContextObject);
}

void UUIHelperLibrary::ShowLobbyErrorPopup(const FString& ErrorMessage, UObject* WorldContextObject)
{
	ShowErrorPopup("Lobby Error", ErrorMessage, WorldContextObject);
}

TSubclassOf<UErrorWidgetBase> UUIHelperLibrary::GetErrorWidgetClass()
{
	if (UCustomUISettings* CustomUISettings = GetMutableDefault<UCustomUISettings>())
	{
		return CustomUISettings->ErrorWidgetClass;
	}
	return nullptr;
}

bool UUIHelperLibrary::IsErrorWidgetConfigured()
{
	TSubclassOf<UErrorWidgetBase> ErrorWidgetClass = GetErrorWidgetClass();
	return ErrorWidgetClass != nullptr;
}


void UUIHelperLibrary::OpenSteamOverlay(UObject* WorldContextObject)
{
	if (SteamFriends())
	{
		SteamFriends()->ActivateGameOverlay("Friends");
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Steam Overlay is not available."));
	}
}

int UUIHelperLibrary::GetPlayerCount(UObject* WorldContextObject)
{
	if (!IsValid(WorldContextObject)) return 0;

	UStormEscapeGameInstance* GI = Cast<UStormEscapeGameInstance>(WorldContextObject->GetWorld()->GetGameInstance());
	if (IsValid(GI))
	{
		return GI->GetCurrentLobbyData().CurrentPlayers;
	}

	return UGameplayStatics::GetNumPlayerStates(WorldContextObject);
}

const FNamedOnlineSession* UUIHelperLibrary::GetSession(UObject* WorldContextObject)
{
	if (!IsValid(WorldContextObject)) return nullptr;

	IOnlineSessionPtr sessionInterface = Online::GetSessionInterface(WorldContextObject->GetWorld());
	return sessionInterface.IsValid() ? sessionInterface->GetNamedSession("StormEscape") : nullptr;
}
