# Error Widget Quick Reference

## 🚀 Quick Setup (5 Minutes)

### 1. Create Widget Blueprint
1. **Right-click** → **User Interface** → **Widget Blueprint**
2. **Name:** `WBP_ErrorWidget`
3. **Reparent to:** `Error Widget Base`

### 2. Add Required Widgets (Exact Names!)
- `ErrorHead` - **Text Block** (title)
- `ErrorBody` - **Text Block** (message)
- `ConfirmButton` - **Common Button Base** (OK button)
- `CancelButton` - **Common Button Base** (Cancel button)

### 3. Configure in Project Settings
- **Project Settings** → **Custom UI Settings** → **Error Widget Class** → `WBP_ErrorWidget`

### 4. Done! ✅
Your error widget is ready to use!

---

## 📋 Widget Properties (Set in Details Panel)

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `Default Confirm Text` | Text | "OK" | Text for confirm button |
| `Default Cancel Text` | Text | "Cancel" | Text for cancel button |
| `Show Confirm Button By Default` | Bool | True | Show confirm button |
| `Show Cancel Button By Default` | Bool | False | Show cancel button |
| `Close On Confirm` | Bool | True | Auto-close on confirm |
| `Close On Cancel` | Bool | True | Auto-close on cancel |

---

## 🎮 Blueprint Functions (Use in Any Blueprint)

### Simple Functions
```
Show Simple Error Popup
├─ Error Message: "Something went wrong!"
└─ World Context Object: Self
```

```
Show Network Error Popup
├─ Error Message: "Connection failed"
└─ World Context Object: Self
```

```
Show Lobby Error Popup  
├─ Error Message: "Failed to join lobby"
└─ World Context Object: Self
```

### Advanced Functions
```
Show Error Popup Advanced
├─ Error Head: "Confirmation"
├─ Error Body: "Are you sure?"
├─ World Context Object: Self
├─ Show Confirm Button: True
├─ Show Cancel Button: True
├─ Confirm Text: "Yes"
└─ Cancel Text: "No"
```

```
Show Confirmation Dialog
├─ Title: "Delete Save"
├─ Message: "This cannot be undone"
├─ World Context Object: Self
├─ Confirm Text: "Delete"
└─ Cancel Text: "Keep"
```

---

## 🔧 Blueprint Events (Override in Your Widget)

### Button Events
- **`BP On Confirm Clicked`** - User clicked confirm/OK
- **`BP On Cancel Clicked`** - User clicked cancel
- **`BP On Handle Back Action`** - User pressed gamepad back (B/Circle) - inherited from CommonUI

### Lifecycle Events
- **`BP On Error Widget Initialized`** - Widget was set up with text
- **`BP On Error Widget Activated`** - Widget became active
- **`BP On Error Widget Deactivated`** - Widget was closed

### Event Delegates (Bind from Other Blueprints)
- **`On Error Confirm`** - Broadcast when confirm clicked
- **`On Error Cancel`** - Broadcast when cancel clicked
- **`On Error Closed`** - Broadcast when widget closed

---

## 💡 Common Usage Patterns

### Basic Error
```
Event BeginPlay
└─ Show Simple Error Popup
   ├─ Error Message: "Failed to load save file"
   └─ World Context Object: Self
```

### Confirmation Dialog
```
Event On Delete Button Clicked
└─ Show Confirmation Dialog
   ├─ Title: "Delete Item"
   ├─ Message: "Are you sure you want to delete this item?"
   ├─ World Context Object: Self
   ├─ Confirm Text: "Delete"
   └─ Cancel Text: "Keep"
```

### Network Error with Retry
```
Event On Network Failed
├─ Show Error Popup Advanced
│  ├─ Error Head: "Connection Failed"
│  ├─ Error Body: "Unable to connect to server"
│  ├─ World Context Object: Self
│  ├─ Show Confirm Button: True
│  ├─ Show Cancel Button: True
│  ├─ Confirm Text: "Retry"
│  └─ Cancel Text: "Offline Mode"
├─ Bind Event to On Error Confirm
│  └─ Event: Retry Connection
└─ Bind Event to On Error Cancel
   └─ Event: Enter Offline Mode
```

---

## 🎨 Styling Tips

### Widget Hierarchy
```
WBP_ErrorWidget
└─ Canvas Panel (Root)
    ├─ Background (Image/Border)
    ├─ Content Container (Vertical Box)
    │   ├─ ErrorHead (Text Block)
    │   ├─ ErrorBody (Text Block)
    │   └─ Button Container (Horizontal Box)
    │       ├─ ConfirmButton (Common Button Base)
    │       └─ CancelButton (Common Button Base)
    └─ Close Button (Optional)
```

### Design Guidelines
- **Use consistent spacing** (16px margins recommended)
- **Make text readable** (minimum 14pt font size)
- **Add background blur** for modal feel
- **Use CommonUI styles** for consistency
- **Test with gamepad** navigation

---

## 🎮 Gamepad Support (Automatic!)

| Input | Action |
|-------|--------|
| **D-Pad/Left Stick** | Navigate between buttons |
| **A/X Button** | Activate focused button |
| **B/Circle Button** | Trigger back action |
| **Auto Focus** | Confirm button gets focus automatically |

---

## 🐛 Troubleshooting

| Problem | Solution |
|---------|----------|
| Widget not showing | Check Error Widget Class in Project Settings |
| Buttons not working | Verify exact widget names: `ConfirmButton`, `CancelButton` |
| No gamepad navigation | Ensure buttons are focusable and CommonUI is configured |
| Events not firing | Check event names match exactly |
| Wrong button text | Set Default Confirm/Cancel Text in widget properties |

---

## 📁 File Locations

### Your Files
- **Widget Blueprint:** `Content/[YourPath]/WBP_ErrorWidget.uasset`
- **Project Settings:** Edit → Project Settings → Custom UI Settings

### System Files
- **Base Class:** `Source/StormEscape/Public/UI/ErrorWidgetBase.h`
- **Helper Functions:** `Source/StormEscape/Public/BlueprintLibraries/UIHelperLibrary.h`

---

## ✅ Checklist

- [ ] Created widget Blueprint based on Error Widget Base
- [ ] Added required widgets with exact names
- [ ] Set Error Widget Class in Project Settings  
- [ ] Tested with simple error popup
- [ ] Verified gamepad navigation works
- [ ] Styled to match game's visual design
- [ ] Tested error scenarios in lobby/network code

---

## 🚀 Next Steps

1. **Create your error widget** following the quick setup
2. **Test it** with `Show Simple Error Popup`
3. **Style it** to match your game
4. **Replace existing error calls** in lobby/network code
5. **Add custom behavior** with Blueprint events
6. **Test with gamepad** to ensure navigation works

**That's it!** Your error widget system is ready for production use! 🎉
