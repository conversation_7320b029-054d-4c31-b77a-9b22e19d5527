// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "OnlineSessionSettings.h"
#include "UIHelperLibrary.generated.h"

class UBaseMainLayerUI;
class UPopupErrorWidget;
/**
 * 
 */
UCLASS()
class STORMESCAPE_API UUIHelperLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	UFUNCTION(BlueprintPure, Category = "Game Config")
	static FString GetProjectVersion();

	UFUNCTION(BlueprintPure, Category = "Regex")
	static bool MatchesRegex(const FString& Input, const FString& Pattern);
	
	UFUNCTION(BlueprintCallable, meta = (WorldContext = "WorldContextObject"), Category = "UI")
	static void ShowErrorPopup(const FString& ErrorHead, const FString& ErrorBody, UObject* WorldContextObject);

	// Enhanced error popup with button configuration
	UFUNCTION(BlueprintCallable, meta = (WorldContext = "WorldContextObject"), Category = "UI")
	static void ShowErrorPopupAdvanced(const FString& ErrorHead, const FString& ErrorBody,
		UObject* WorldContextObject, bool bShowConfirmButton = true, bool bShowCancelButton = false,
		const FString& ConfirmText = "OK", const FString& CancelText = "Cancel");

	// Show a simple confirmation dialog
	UFUNCTION(BlueprintCallable, meta = (WorldContext = "WorldContextObject"), Category = "UI")
	static void ShowConfirmationDialog(const FString& Title, const FString& Message,
		UObject* WorldContextObject, const FString& ConfirmText = "Yes", const FString& CancelText = "No");

	UFUNCTION(BlueprintCallable, meta = (WorldContext = "WorldContextObject"), Category = "Steam")
	static void OpenSteamOverlay(UObject* WorldContextObject);

	UFUNCTION(BlueprintPure, meta = (WorldContext = "WorldContextObject"), Category = "Session")
	static int GetPlayerCount(UObject* WorldContextObject);

private:
	static const FNamedOnlineSession* GetSession(UObject* WorldContextObject);
};
