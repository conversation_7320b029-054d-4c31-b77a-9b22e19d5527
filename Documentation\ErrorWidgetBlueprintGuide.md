# Error Widget Blueprint Implementation Guide

This guide shows how to easily create and customize error widgets in the Unreal Widget Editor (Blueprint) without needing C++ knowledge.

## Quick Start - Creating an Error Widget in Blueprint

### Step 1: Create the Widget Blueprint

1. **Right-click in Content Browser** → **User Interface** → **Widget Blueprint**
2. **Name it** `WBP_ErrorWidget` (or your preferred name)
3. **Open the Widget Blueprint**
4. **In the Graph tab**, click **"Reparent Blueprint"**
5. **Search for and select** `Error Widget Base`

### Step 2: Design the Widget Layout

In the **Designer** tab:

1. **Add a Canvas Panel** as the root widget
2. **Add these widgets** with the exact names (use "Bind Widget"):
   - `ErrorHead` - **Text Block** for the title
   - `ErrorBody` - **Text Block** for the message  
   - `ConfirmButton` - **Common Button Base** for OK/Confirm
   - `CancelButton` - **Common Button Base** for Cancel (optional)

3. **Style your widgets** as desired (fonts, colors, sizes, etc.)

### Step 3: Configure Widget Settings

In the **Details** panel when the widget is selected:

**Error Widget Settings:**
- `Default Confirm Text` - Text for confirm button (default: "OK")
- `Default Cancel Text` - Text for cancel button (default: "Cancel")  
- `Show Confirm Button By Default` - Whether to show confirm button
- `Show Cancel Button By Default` - Whether to show cancel button
- `Close On Confirm` - Auto-close when confirm is clicked
- `Close On Cancel` - Auto-close when cancel is clicked

### Step 4: Set Up Button Events (Optional)

In the **Graph** tab, you can override these events:

#### Basic Events
- **BP On Confirm Clicked** - Called when confirm button is pressed
- **BP On Cancel Clicked** - Called when cancel button is pressed
- **BP On Handle Back Action** - Called when gamepad back button (B/Circle) is pressed (inherited from CommonUI)

#### Lifecycle Events  
- **BP On Error Widget Initialized** - Called when widget is set up with text
- **BP On Error Widget Activated** - Called when widget becomes active
- **BP On Error Widget Deactivated** - Called when widget is closed

#### Event Delegates (Bind in other Blueprints)
- **On Error Confirm** - Broadcast when confirm is clicked
- **On Error Cancel** - Broadcast when cancel is clicked  
- **On Error Closed** - Broadcast when widget is closed

### Step 5: Configure in Project Settings

1. **Edit** → **Project Settings**
2. **Search for** "Custom UI Settings"
3. **Set Error Widget Class** to your `WBP_ErrorWidget`

## Usage Examples

### Simple Error (Blueprint)
```
Show Simple Error Popup
├─ Error Message: "Something went wrong!"
└─ World Context Object: Self
```

### Network Error (Blueprint)
```
Show Network Error Popup  
├─ Error Message: "Connection failed"
└─ World Context Object: Self
```

### Lobby Error (Blueprint)
```
Show Lobby Error Popup
├─ Error Message: "Failed to join lobby"
└─ World Context Object: Self
```

### Advanced Error (Blueprint)
```
Show Error Popup Advanced
├─ Error Head: "Confirmation"
├─ Error Body: "Are you sure you want to quit?"
├─ World Context Object: Self
├─ Show Confirm Button: True
├─ Show Cancel Button: True  
├─ Confirm Text: "Yes"
└─ Cancel Text: "No"
```

### Confirmation Dialog (Blueprint)
```
Show Confirmation Dialog
├─ Title: "Delete Save"
├─ Message: "This cannot be undone"
├─ World Context Object: Self
├─ Confirm Text: "Delete"
└─ Cancel Text: "Keep"
```

## Advanced Customization

### Custom Button Behavior

Override **BP On Confirm Clicked**:
```
Event BP On Confirm Clicked
├─ [Your custom logic here]
├─ Print String: "User confirmed!"
└─ [Don't call parent - widget auto-closes if Close On Confirm is true]
```

Override **BP On Cancel Clicked**:
```
Event BP On Cancel Clicked  
├─ [Your custom logic here]
├─ Print String: "User cancelled!"
└─ [Don't call parent - widget auto-closes if Close On Cancel is true]
```

### Custom Back Button Behavior

Override **BP On Handle Back Action**:
```
Event BP On Handle Back Action
├─ [Your custom logic here]
├─ Print String: "Back button pressed!"
├─ Close Error Widget (if you want to close)
└─ Return Value: True (we handled the action)
```

### Binding to Events from Other Blueprints

In your **Game Mode**, **Player Controller**, or other Blueprint:

```
Show Error Popup Advanced
├─ [Configure parameters]
├─ Return Value → Bind Event to On Error Confirm
│   └─ Event: Handle User Confirmed
└─ Return Value → Bind Event to On Error Cancel
    └─ Event: Handle User Cancelled
```

### Dynamic Text and Styling

Override **BP On Error Widget Initialized**:
```
Event BP On Error Widget Initialized
├─ Get Error Head → Set Font Size: 24
├─ Get Error Body → Set Font Size: 16
├─ Get Confirm Button → Set Style: "ConfirmButtonStyle"
└─ Get Cancel Button → Set Style: "CancelButtonStyle"
```

## Widget Hierarchy Example

```
WBP_ErrorWidget (Error Widget Base)
└─ Canvas Panel
    ├─ Background Image
    ├─ Content Vertical Box
    │   ├─ ErrorHead (Text Block)
    │   ├─ ErrorBody (Text Block)  
    │   └─ Button Horizontal Box
    │       ├─ ConfirmButton (Common Button Base)
    │       └─ CancelButton (Common Button Base)
    └─ Close Button (Optional)
```

## Gamepad Support

Your error widget automatically supports gamepad navigation:

- **D-Pad/Left Stick** - Navigate between buttons
- **A/X Button** - Activate focused button
- **B/Circle Button** - Triggers back action (calls BP On Handle Back Action)
- **Focus** - Automatically set to confirm button when widget opens

## Styling Tips

### Making it Look Professional

1. **Use consistent spacing** - Set margins and padding uniformly
2. **Choose readable fonts** - Ensure text is clear at different sizes
3. **Add background blur** - Use a semi-transparent background
4. **Animate entrance** - Add fade-in or slide-in animations
5. **Use CommonUI styles** - Leverage CommonUI's styling system

### Responsive Design

1. **Use anchors** - Set proper anchors for different screen sizes
2. **Size to content** - Let text blocks size themselves
3. **Test on different resolutions** - Ensure it looks good everywhere

## Common Patterns

### Network Error with Retry
```
Override BP On Confirm Clicked:
├─ Attempt Reconnection
├─ If Success: Close Widget
└─ If Fail: Show New Error "Still can't connect"
```

### Confirmation with Consequences
```
Override BP On Confirm Clicked:
├─ Save Game Data
├─ Delete Old Save
├─ Show Success Message
└─ Close Widget
```

### Multi-Step Error Handling
```
Override BP On Cancel Clicked:
├─ Show Confirmation Dialog: "Are you sure you want to cancel?"
├─ Bind to Confirm: Actually Cancel
└─ Bind to Cancel: Return to Original Error
```

## Troubleshooting

### Widget Not Showing
- Check that Error Widget Class is set in Project Settings
- Verify World Context Object is valid
- Check that widget has proper parent class (Error Widget Base)

### Buttons Not Working  
- Ensure button names match exactly: `ConfirmButton`, `CancelButton`
- Check that buttons are set to "Bind Widget" in details panel
- Verify buttons are visible and enabled

### Gamepad Navigation Issues
- Make sure buttons are focusable (Is Focusable = true)
- Check that CommonUI is properly configured
- Verify input mapping contexts are set up correctly

### Events Not Firing
- Check that event names match exactly
- Ensure you're not calling parent events when you shouldn't
- Verify delegate bindings are set up correctly

## Best Practices

1. **Keep it simple** - Don't overcomplicate the error widget
2. **Test with gamepad** - Always verify gamepad navigation works
3. **Use consistent styling** - Match your game's visual style
4. **Provide clear actions** - Make it obvious what each button does
5. **Handle edge cases** - What happens if no buttons are shown?
6. **Test error scenarios** - Make sure errors actually show the widget

This system makes it easy for designers and Blueprint users to create professional error widgets without touching C++ code!
