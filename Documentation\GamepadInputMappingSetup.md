# Gamepad Input Mapping Context Setup

This document explains how to set up and use the new gamepad input mapping context system in StormEscape.

## Overview

The system automatically switches between two input mapping contexts based on the UI state:
- **UI Controls Input Mapping** (`IM_UIControls.uasset`) - Used when UI is active
- **Game Controls Input Mapping** (`IMC_FirstPersonMappingContext.uasset`) - Used during gameplay

## Setup Instructions

### 1. Configure Input Mapping Contexts in Blueprint

1. Open your `StormEscapePlayerController` Blueprint (or create one if using C++ only)
2. In the **Input** section, you'll find two new properties:
   - **UI Controls Input Mapping**: Set this to `/Game/StormEscape/Input/Mappings/IM_UIControls`
   - **Game Controls Input Mapping**: Set this to `/Game/StormEscape/Input/IMC_FirstPersonMappingContext`

### 2. Input Mapping Context Assets

Make sure your input mapping context assets are properly configured:

#### IM_UIControls.uasset
- Should contain gamepad bindings for UI navigation
- Common bindings include:
  - D-Pad/Left Stick for navigation
  - Face buttons for selection/back
  - Shoulder buttons for tab switching

#### IMC_FirstPersonMappingContext.uasset
- Should contain gamepad bindings for gameplay
- Common bindings include:
  - Left stick for movement
  - Right stick for camera
  - Triggers for actions
  - Face buttons for jump, interact, etc.

## How It Works

### Automatic Context Switching

The system automatically manages input mapping contexts when UI state changes:

1. **UIOnly Mode**: 
   - Enables UI input mapping context (high priority: 100)
   - Disables game input mapping context
   - Used for menus, popups, modals

2. **GameOnly Mode**:
   - Disables UI input mapping context
   - Enables game input mapping context (normal priority: 0)
   - Used during normal gameplay

3. **GameAndUI Mode**:
   - Enables both contexts
   - UI context has higher priority (100) than game context (0)
   - Used for in-game UI that doesn't block gameplay

### Integration with UIManager

The system integrates seamlessly with the existing UIManager:

```cpp
// This automatically handles input mapping contexts
UUIManager::AddWidgetToStack(this, WidgetClass, EWidgetLayer::Menu, EInputModeType::UIOnly, true);

// Or manually set input mode
UUIManager::SetInputMode(this, EInputModeType::GameOnly, false);
```

### Manual Control (Advanced)

You can also manually control input mapping contexts:

```cpp
// Enable/disable UI input mapping
PlayerController->SetUIInputMappingContext(true);

// Enable/disable game input mapping  
PlayerController->SetGameInputMappingContext(false);
```

## Priority System

Input mapping contexts use a priority system:
- **UI Context Priority**: 100 (higher priority)
- **Game Context Priority**: 0 (lower priority)

When both contexts are active, the UI context takes precedence for conflicting inputs.

## Troubleshooting

### Common Issues

1. **Gamepad not working in UI**:
   - Verify `IM_UIControls.uasset` is assigned to the PlayerController
   - Check that the UI input mapping contains gamepad bindings
   - Ensure CommonUI is properly configured for gamepad support

2. **Gamepad not working in game**:
   - Verify `IMC_FirstPersonMappingContext.uasset` is assigned to the PlayerController
   - Check that the game input mapping contains gamepad bindings

3. **Input conflicts**:
   - Review priority settings
   - Ensure UI and game contexts don't have conflicting bindings
   - Consider using different input actions for UI vs game

### Debug Logging

The system includes debug logging. Look for these messages in the console:
- "Added UI Input Mapping Context"
- "Removed UI Input Mapping Context"  
- "Added Game Input Mapping Context"
- "Removed Game Input Mapping Context"

## Best Practices

1. **Separate Input Actions**: Use different input actions for UI and gameplay to avoid conflicts
2. **Test Both Modes**: Always test gamepad functionality in both UI and gameplay modes
3. **Priority Management**: Keep UI context priority higher than game context
4. **Consistent Bindings**: Use consistent gamepad button mappings across similar UI elements

## Example Configuration

### UI Input Actions (IM_UIControls)
- Navigate: Gamepad Left Stick / D-Pad
- Confirm: Gamepad Face Button Bottom (A/X)
- Cancel: Gamepad Face Button Right (B/Circle)
- Tab Left: Gamepad Left Shoulder
- Tab Right: Gamepad Right Shoulder

### Game Input Actions (IMC_FirstPersonMappingContext)
- Move: Gamepad Left Stick
- Look: Gamepad Right Stick
- Jump: Gamepad Face Button Bottom (A/X)
- Interact: Gamepad Face Button Right (B/Circle)
- Sprint: Gamepad Left Shoulder
- Crouch: Gamepad Right Shoulder
