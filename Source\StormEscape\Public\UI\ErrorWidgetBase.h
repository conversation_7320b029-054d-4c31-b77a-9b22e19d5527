// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Components/TextBlock.h"
#include "CommonButtonBase.h"
#include "ErrorWidgetBase.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnErrorClosed);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnErrorConfirm);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnErrorCancel);

/**
 * Enhanced Error Widget with CommonUI gamepad support and improved input handling
 * Uses CommonUI's native input handling and focus management
 * Designed to be easily customizable in Blueprint Widget Editor
 */
UCLASS(BlueprintType, Blueprintable)
class STORMESCAPE_API UErrorWidgetBase : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorHead;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorBody;

	// Optional button widgets for user interaction
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCommonButtonBase* ConfirmButton;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCommonButtonBase* CancelButton;

	// Blueprint Events - Easy to bind in Widget Editor
	UPROPERTY(BlueprintAssignable, Category = "Error Widget Events")
	FOnErrorClosed OnErrorClosed;

	UPROPERTY(BlueprintAssignable, Category = "Error Widget Events")
	FOnErrorConfirm OnErrorConfirm;

	UPROPERTY(BlueprintAssignable, Category = "Error Widget Events")
	FOnErrorCancel OnErrorCancel;

	// Blueprint Configurable Properties - Set these in Widget Editor
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Widget Settings")
	FText DefaultConfirmText = FText::FromString("OK");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Widget Settings")
	FText DefaultCancelText = FText::FromString("Cancel");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Widget Settings")
	bool bShowConfirmButtonByDefault = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Widget Settings")
	bool bShowCancelButtonByDefault = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Widget Settings")
	bool bCloseOnConfirm = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Widget Settings")
	bool bCloseOnCancel = true;

	// Initialize the error widget with text and optional button configuration
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void InitErrorWidget(const FString& ErrorHeadText, const FString& ErrorBodyText,
		bool bShowConfirmButton = true, bool bShowCancelButton = false,
		const FString& ConfirmText = "OK", const FString& CancelText = "Cancel");

	// Simple initialization using default settings
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void InitErrorWidgetSimple(const FString& ErrorHeadText, const FString& ErrorBodyText);

	// Button click handlers - Can be overridden in Blueprint
	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent, Category = "ErrorWidget")
	void BP_OnConfirmClicked();

	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent, Category = "ErrorWidget")
	void BP_OnCancelClicked();

	// C++ handlers that call Blueprint events
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void OnConfirmClicked();

	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void OnCancelClicked();

	// CommonUI input action handlers (override these in Blueprint or C++)
	// Note: BP_OnHandleBackAction is inherited from UCommonActivatableWidget

	// Set the desired focus target for gamepad navigation
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void SetInitialFocus();

	// Blueprint Events for Widget Lifecycle - Easy to override in Blueprint
	UFUNCTION(BlueprintImplementableEvent, Category = "Error Widget Events")
	void BP_OnErrorWidgetInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Error Widget Events")
	void BP_OnErrorWidgetActivated();

	UFUNCTION(BlueprintImplementableEvent, Category = "Error Widget Events")
	void BP_OnErrorWidgetDeactivated();

protected:

	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;
	virtual void NativeOnActivated() override;
	virtual void NativeOnDeactivated() override;

	// CommonUI back action handling
	virtual bool NativeOnHandleBackAction() override;

	// CommonUI desired focus target
	virtual UWidget* NativeGetDesiredFocusTarget() const override;

private:

	// Setup button bindings
	void SetupButtonBindings();

	// Close the error widget
	void CloseErrorWidget();
};
