// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/EnhancedUIWidgetBase.h"
#include "Components/Widget.h"
#include "GameFramework/PlayerController.h"

UEnhancedUIWidgetBase::UEnhancedUIWidgetBase(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	bUseAutomaticInputHelper = true;
	bUseAutomaticFocusManagement = true;
	InitialFocusWidget = nullptr;
	UIInputHelper = nullptr;
}

void UEnhancedUIWidgetBase::NativeConstruct()
{
	Super::NativeConstruct();
	
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: Constructing %s"), *GetClass()->GetName());
	
	if (bUseAutomaticInputHelper)
	{
		InitializeInputHelper();
	}
	
	if (bUseAutomaticFocusManagement)
	{
		SetupInitialFocus();
	}
}

void UEnhancedUIWidgetBase::NativeDestruct()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: Destructing %s"), *GetClass()->GetName());
	
	CleanupInputHelper();
	
	Super::NativeDestruct();
}

void UEnhancedUIWidgetBase::NativeOnActivated()
{
	Super::NativeOnActivated();
	
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: Activated %s"), *GetClass()->GetName());
	
	if (bUseAutomaticInputHelper && UIInputHelper)
	{
		SetupInputHelper();
	}
}

void UEnhancedUIWidgetBase::NativeOnDeactivated()
{
	Super::NativeOnDeactivated();
	
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: Deactivated %s"), *GetClass()->GetName());
	
	if (UIInputHelper)
	{
		CleanupInputHelper();
	}
}

void UEnhancedUIWidgetBase::HandleUIConfirm()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: UI Confirm handled"));
	OnUIConfirmPressed();
}

void UEnhancedUIWidgetBase::HandleUICancel()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: UI Cancel handled"));
	OnUICancelPressed();
}

void UEnhancedUIWidgetBase::HandleUINavigateUp()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: UI Navigate Up handled"));
	OnUINavigateUp();
}

void UEnhancedUIWidgetBase::HandleUINavigateDown()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: UI Navigate Down handled"));
	OnUINavigateDown();
}

void UEnhancedUIWidgetBase::HandleUINavigateLeft()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: UI Navigate Left handled"));
	OnUINavigateLeft();
}

void UEnhancedUIWidgetBase::HandleUINavigateRight()
{
	UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: UI Navigate Right handled"));
	OnUINavigateRight();
}

void UEnhancedUIWidgetBase::SetupInputHelper()
{
	if (UIInputHelper)
	{
		if (APlayerController* PC = GetOwningPlayer())
		{
			UIInputHelper->SetupInputBindings(PC);
		}
	}
}

void UEnhancedUIWidgetBase::CleanupInputHelper()
{
	if (UIInputHelper)
	{
		if (APlayerController* PC = GetOwningPlayer())
		{
			UIInputHelper->CleanupInputBindings(PC);
		}
	}
}

void UEnhancedUIWidgetBase::SetFocusToWidget(UWidget* Widget)
{
	if (Widget)
	{
		Widget->SetFocus();
		UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: Focus set to widget %s"), *Widget->GetClass()->GetName());
	}
}

UWidget* UEnhancedUIWidgetBase::GetCurrentlyFocusedWidget() const
{
	// This is a simplified implementation - in a real scenario you might want to track focus more precisely
	return InitialFocusWidget;
}

void UEnhancedUIWidgetBase::InitializeInputHelper()
{
	if (!UIInputHelper)
	{
		UIInputHelper = UUIInputHelper::CreateUIInputHelper(this);
		if (UIInputHelper)
		{
			BindInputHelperEvents();
			UE_LOG(LogTemp, Log, TEXT("EnhancedUIWidgetBase: Input helper initialized"));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("EnhancedUIWidgetBase: Failed to create input helper"));
		}
	}
}

void UEnhancedUIWidgetBase::BindInputHelperEvents()
{
	if (UIInputHelper)
	{
		UIInputHelper->OnUIConfirm.AddDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperConfirm);
		UIInputHelper->OnUICancel.AddDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperCancel);
		UIInputHelper->OnUINavigateUp.AddDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateUp);
		UIInputHelper->OnUINavigateDown.AddDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateDown);
		UIInputHelper->OnUINavigateLeft.AddDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateLeft);
		UIInputHelper->OnUINavigateRight.AddDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateRight);
	}
}

void UEnhancedUIWidgetBase::UnbindInputHelperEvents()
{
	if (UIInputHelper)
	{
		UIInputHelper->OnUIConfirm.RemoveDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperConfirm);
		UIInputHelper->OnUICancel.RemoveDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperCancel);
		UIInputHelper->OnUINavigateUp.RemoveDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateUp);
		UIInputHelper->OnUINavigateDown.RemoveDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateDown);
		UIInputHelper->OnUINavigateLeft.RemoveDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateLeft);
		UIInputHelper->OnUINavigateRight.RemoveDynamic(this, &UEnhancedUIWidgetBase::OnInputHelperNavigateRight);
	}
}

void UEnhancedUIWidgetBase::SetupInitialFocus()
{
	if (InitialFocusWidget)
	{
		SetFocusToWidget(InitialFocusWidget);
	}
}

void UEnhancedUIWidgetBase::OnInputHelperConfirm()
{
	HandleUIConfirm();
}

void UEnhancedUIWidgetBase::OnInputHelperCancel()
{
	HandleUICancel();
}

void UEnhancedUIWidgetBase::OnInputHelperNavigateUp()
{
	HandleUINavigateUp();
}

void UEnhancedUIWidgetBase::OnInputHelperNavigateDown()
{
	HandleUINavigateDown();
}

void UEnhancedUIWidgetBase::OnInputHelperNavigateLeft()
{
	HandleUINavigateLeft();
}

void UEnhancedUIWidgetBase::OnInputHelperNavigateRight()
{
	HandleUINavigateRight();
}
