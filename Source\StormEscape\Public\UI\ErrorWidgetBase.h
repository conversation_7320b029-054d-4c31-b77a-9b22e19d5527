// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Components/TextBlock.h"
#include "CommonButtonBase.h"
#include "ErrorWidgetBase.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnErrorClosed);

/**
 * Enhanced Error Widget with CommonUI gamepad support and improved input handling
 * Uses CommonUI's native input handling and focus management
 */
UCLASS()
class STORMESCAPE_API UErrorWidgetBase : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorHead;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorBody;

	// Optional button widgets for user interaction
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCommonButtonBase* ConfirmButton;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCommonButtonBase* CancelButton;

	UPROPERTY(BlueprintAssignable, Category = "ErrorWidget")
	FOnErrorClosed OnErrorClosed;

	// Initialize the error widget with text and optional button configuration
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void InitErrorWidget(const FString& ErrorHeadText, const FString& ErrorBodyText,
		bool bShowConfirmButton = true, bool bShowCancelButton = false,
		const FString& ConfirmText = "OK", const FString& CancelText = "Cancel");

	// Button click handlers
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void OnConfirmClicked();

	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void OnCancelClicked();

	// CommonUI input action handlers (override these in Blueprint or C++)
	UFUNCTION(BlueprintImplementableEvent, Category = "ErrorWidget")
	bool BP_OnHandleBackAction();

	// Set the desired focus target for gamepad navigation
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void SetInitialFocus();

protected:

	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;
	virtual void NativeOnActivated() override;
	virtual void NativeOnDeactivated() override;

	// CommonUI back action handling
	virtual bool NativeOnHandleBackAction() override;

	// CommonUI desired focus target
	virtual UWidget* NativeGetDesiredFocusTarget() const override;

private:

	// Setup button bindings
	void SetupButtonBindings();

	// Close the error widget
	void CloseErrorWidget();
};
