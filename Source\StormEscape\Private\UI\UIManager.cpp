#include "UI/UIManager.h"

#include "CommonActivatableWidget.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "Core/StormEscapeGameInstance.h"
#include "StormEscapePlayerController.h"
#include "UI/BaseMainLayerUI.h"
#include "Widgets/CommonActivatableWidgetContainer.h"


TWeakObjectPtr<UBaseMainLayerUI> UUIManager::MainLayerUI = nullptr;
TWeakObjectPtr<APlayerController> UUIManager::CachedPC = nullptr;

void UUIManager::Initialize(APlayerController* PlayerController, UBaseMainLayerUI* MainLayerUIInstance)
{
	MainLayerUI = MainLayerUIInstance;
	CachedPC = PlayerController;

	UE_LOG(LogTemp, Warning, TEXT("UIManager Initialized"));

	if (!PlayerController)
	{
		return;
	}

	//GetGameInstance and check if bWasKicked is true and if so call the error popup
	if (UStormEscapeGameInstance* GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(PlayerController))
	{
		if (GameInstance->bWasKicked)
		{
			UE_LOG(LogTemp, Warning, TEXT("Player was kicked. Showing error popup."));
			UUIHelperLibrary::ShowErrorPopup("Disconnected",GameInstance->KickReason, PlayerController);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to get GameInstance from PlayerController"));
	}
}

UCommonActivatableWidget* UUIManager::AddWidgetToStack(UObject* WorldContextObject,
	TSubclassOf<UCommonActivatableWidget> WidgetClass,
	EWidgetLayer TargetLayer,
	EInputModeType InputMode,
	bool bShowMouseCursor,
	bool bClearCurrentStack)
{
	UE_LOG(LogTemp, Warning, TEXT("Adding widget to stack"));
	if (!MainLayerUI.IsValid() || !CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UUIManager not initialized. Call Initialize() from PlayerController."));
		return nullptr;
	}

	UCommonActivatableWidgetStack* TargetStack = GetTargetStack(TargetLayer);
	if (!TargetStack)
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid widget stack for layer %d"), (int32)TargetLayer);
		return nullptr;
	}

	if (bClearCurrentStack)
	{
		TargetStack->ClearWidgets();
	}

	UCommonActivatableWidget* Widget = TargetStack->AddWidget(WidgetClass);

	// Set input mode
	switch (InputMode)
	{
	case EInputModeType::UIOnly:
		UWidgetBlueprintLibrary::SetInputMode_UIOnlyEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	case EInputModeType::GameOnly:
		UWidgetBlueprintLibrary::SetInputMode_GameOnly(CachedPC.Get());
		break;
	case EInputModeType::GameAndUI:
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	}

	// Manage input mapping contexts for gamepad support
	ManageInputMappingContexts(CachedPC.Get(), InputMode);

	CachedPC->bShowMouseCursor = bShowMouseCursor;

	return Widget;
}

void UUIManager::SetInputMode(UObject* WorldContextObject, EInputModeType InputMode, bool bShowMouseCursor,UUserWidget* Widget)
{
	if (!MainLayerUI.IsValid() || !CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UUIManager not initialized. Call Initialize() from PlayerController."));
		return;
	}

	// Set input mode
	switch (InputMode)
	{
	case EInputModeType::UIOnly:
		UWidgetBlueprintLibrary::SetInputMode_UIOnlyEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	case EInputModeType::GameOnly:
		UWidgetBlueprintLibrary::SetInputMode_GameOnly(CachedPC.Get());
		break;
	case EInputModeType::GameAndUI:
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	}

	// Manage input mapping contexts for gamepad support
	ManageInputMappingContexts(CachedPC.Get(), InputMode);

	CachedPC->bShowMouseCursor = bShowMouseCursor;
}

UCommonActivatableWidgetStack* UUIManager::GetTargetStack(EWidgetLayer Layer)
{
	if (!MainLayerUI.IsValid())
		return nullptr;

	switch (Layer)
	{
	case EWidgetLayer::GameUI:
		return MainLayerUI->GameUIStack;
	case EWidgetLayer::Menu:
		return MainLayerUI->MenuStack;
	case EWidgetLayer::Popup:
		return MainLayerUI->PopupStack;
	case EWidgetLayer::Modal:
		return MainLayerUI->ModalStack;
	default:
		return nullptr;
	}
}

void UUIManager::ManageInputMappingContexts(APlayerController* PlayerController, EInputModeType InputMode)
{
	if (!PlayerController || !PlayerController->IsLocalController())
	{
		return;
	}

	// Cast to StormEscapePlayerController to access input mapping context functions
	if (AStormEscapePlayerController* StormPC = Cast<AStormEscapePlayerController>(PlayerController))
	{
		switch (InputMode)
		{
		case EInputModeType::UIOnly:
			// Enable UI input mapping, disable game input mapping
			StormPC->SetUIInputMappingContext(true);
			StormPC->SetGameInputMappingContext(false);
			break;
		case EInputModeType::GameOnly:
			// Disable UI input mapping, enable game input mapping
			StormPC->SetUIInputMappingContext(false);
			StormPC->SetGameInputMappingContext(true);
			break;
		case EInputModeType::GameAndUI:
			// Enable both input mappings (UI has higher priority)
			StormPC->SetUIInputMappingContext(true);
			StormPC->SetGameInputMappingContext(true);
			break;
		}
	}
}
