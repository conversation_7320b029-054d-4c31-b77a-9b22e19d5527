// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/ErrorWidgetBase.h"
#include "EnhancedInputComponent.h"
#include "Components/TextBlock.h"
#include "CommonButtonBase.h"
#include "UI/UIManager.h"

void UErrorWidgetBase::InitErrorWidget(const FString& ErrorHeadText, const FString& ErrorBodyText,
	bool bShowConfirmButton, bool bShowCancelButton, const FString& ConfirmText, const FString& CancelText)
{
	// Set error text
	if (ErrorHead)
	{
		ErrorHead->SetText(FText::FromString(ErrorHeadText));
	}

	if (ErrorBody)
	{
		ErrorBody->SetText(FText::FromString(ErrorBodyText));
	}

	// Configure buttons
	if (ConfirmButton)
	{
		ConfirmButton->SetVisibility(bShowConfirmButton ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (bShowConfirmButton)
		{
			ConfirmButton->SetButtonText(FText::FromString(ConfirmText));
		}
	}

	if (CancelButton)
	{
		CancelButton->SetVisibility(bShowCancelButton ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (bShowCancelButton)
		{
			CancelButton->SetButtonText(FText::FromString(CancelText));
		}
	}
}

void UErrorWidgetBase::OnConfirmClicked()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Confirm button clicked"));
	CloseErrorWidget();
}

void UErrorWidgetBase::OnCancelClicked()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Cancel button clicked"));
	CloseErrorWidget();
}

void UErrorWidgetBase::HandleUIConfirm()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: UI Confirm input triggered"));
	OnConfirmClicked();
}

void UErrorWidgetBase::HandleUICancel()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: UI Cancel input triggered"));
	OnCancelClicked();
}

void UErrorWidgetBase::NativeConstruct()
{
	Super::NativeConstruct();

	UE_LOG(LogTemp, Log, TEXT("Error Widget: Constructing"));

	SetupButtonBindings();

	// Set initial focus to confirm button if available (override the base class behavior)
	if (ConfirmButton && ConfirmButton->GetVisibility() == ESlateVisibility::Visible)
	{
		InitialFocusWidget = ConfirmButton;
		ConfirmButton->SetFocus();
	}
}

void UErrorWidgetBase::NativeDestruct()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Destructing"));

	OnErrorClosed.Broadcast();

	Super::NativeDestruct();
}

void UErrorWidgetBase::NativeOnActivated()
{
	Super::NativeOnActivated();
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Activated"));
}

void UErrorWidgetBase::NativeOnDeactivated()
{
	Super::NativeOnDeactivated();
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Deactivated"));
}

void UErrorWidgetBase::SetupButtonBindings()
{
	// Bind button click events
	if (ConfirmButton)
	{
		ConfirmButton->OnClicked().AddUObject(this, &UErrorWidgetBase::OnConfirmClicked);
	}

	if (CancelButton)
	{
		CancelButton->OnClicked().AddUObject(this, &UErrorWidgetBase::OnCancelClicked);
	}
}

// Input binding is now handled by the base class EnhancedUIWidgetBase

void UErrorWidgetBase::CloseErrorWidget()
{
	UE_LOG(LogTemp, Log, TEXT("Error Widget: Closing"));

	// Use the CommonActivatableWidget's deactivation system
	DeactivateWidget();
}
