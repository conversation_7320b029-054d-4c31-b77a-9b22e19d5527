// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "UI/UIInputHelper.h"
#include "EnhancedUIWidgetBase.generated.h"

/**
 * Enhanced base UI widget class with built-in gamepad support and input mapping context management
 * All UI widgets should inherit from this class to get automatic gamepad support
 */
UCLASS(BlueprintType, Blueprintable)
class STORMESCAPE_API UEnhancedUIWidgetBase : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:
	UEnhancedUIWidgetBase(const FObjectInitializer& ObjectInitializer);

	// Enable/disable automatic input helper setup
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced UI")
	bool bUseAutomaticInputHelper = true;

	// Enable/disable automatic focus management
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced UI")
	bool bUseAutomaticFocusManagement = true;

	// The widget to focus when this widget is activated (optional)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced UI", meta = (BindWidget))
	UWidget* InitialFocusWidget;

	// UI Input Helper for gamepad support
	UPROPERTY(BlueprintReadOnly, Category = "Enhanced UI")
	UUIInputHelper* UIInputHelper;

	// Override these in Blueprint or C++ to handle UI input events
	UFUNCTION(BlueprintImplementableEvent, Category = "Enhanced UI Events")
	void OnUIConfirmPressed();

	UFUNCTION(BlueprintImplementableEvent, Category = "Enhanced UI Events")
	void OnUICancelPressed();

	UFUNCTION(BlueprintImplementableEvent, Category = "Enhanced UI Events")
	void OnUINavigateUp();

	UFUNCTION(BlueprintImplementableEvent, Category = "Enhanced UI Events")
	void OnUINavigateDown();

	UFUNCTION(BlueprintImplementableEvent, Category = "Enhanced UI Events")
	void OnUINavigateLeft();

	UFUNCTION(BlueprintImplementableEvent, Category = "Enhanced UI Events")
	void OnUINavigateRight();

	// C++ event handlers (can be overridden in derived classes)
	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	virtual void HandleUIConfirm();

	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	virtual void HandleUICancel();

	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	virtual void HandleUINavigateUp();

	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	virtual void HandleUINavigateDown();

	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	virtual void HandleUINavigateLeft();

	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	virtual void HandleUINavigateRight();

	// Manually setup input helper (if automatic setup is disabled)
	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	void SetupInputHelper();

	// Manually cleanup input helper
	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	void CleanupInputHelper();

	// Set focus to a specific widget
	UFUNCTION(BlueprintCallable, Category = "Enhanced UI")
	void SetFocusToWidget(UWidget* Widget);

	// Get the currently focused widget
	UFUNCTION(BlueprintPure, Category = "Enhanced UI")
	UWidget* GetCurrentlyFocusedWidget() const;

protected:
	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;
	virtual void NativeOnActivated() override;
	virtual void NativeOnDeactivated() override;

private:
	// Setup UI input helper and bind events
	void InitializeInputHelper();

	// Bind UI input helper events to our handlers
	void BindInputHelperEvents();

	// Unbind UI input helper events
	void UnbindInputHelperEvents();

	// Setup initial focus
	void SetupInitialFocus();

	// Event handlers for UI input helper
	UFUNCTION()
	void OnInputHelperConfirm();

	UFUNCTION()
	void OnInputHelperCancel();

	UFUNCTION()
	void OnInputHelperNavigateUp();

	UFUNCTION()
	void OnInputHelperNavigateDown();

	UFUNCTION()
	void OnInputHelperNavigateLeft();

	UFUNCTION()
	void OnInputHelperNavigateRight();
};
