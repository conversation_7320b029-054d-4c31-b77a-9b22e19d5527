# CommonUI System Quick Reference

## Quick Setup Checklist

### ✅ **For New UI Widgets (CommonUI Approach)**
1. Inherit from `UCommonActivatableWidget` (the standard approach)
2. Use `UCommonButtonBase` instead of regular buttons
3. Override `NativeOnHandleBackAction()` for back button handling
4. Override `NativeGetDesiredFocusTarget()` for focus management
5. Test with gamepad navigation (works automatically!)

### ✅ **For Error Popups**
```cpp
// Simple error
UUIHelperLibrary::ShowErrorPopup("Title", "Message", this);

// Advanced error with buttons
UUIHelperLibrary::ShowErrorPopupAdvanced("Title", "Message", this, true, true, "OK", "Cancel");

// Confirmation dialog
UUIHelperLibrary::ShowConfirmationDialog("Title", "Message", this, "Yes", "No");
```

### ✅ **For Input Mapping Context Setup**
1. Set UI Controls Input Mapping: `/Game/StormEscape/Input/Mappings/IM_UIControls`
2. Set Game Controls Input Mapping: `/Game/StormEscape/Input/IMC_FirstPersonMappingContext`
3. Configure in StormEscapePlayerController Blueprint
4. CommonUI handles the rest automatically!

## Common Code Patterns

### **Creating CommonUI Widget (C++)**
```cpp
UCLASS()
class UMyWidget : public UCommonActivatableWidget
{
    GENERATED_BODY()

public:
    // Handle gamepad back button (B/Circle)
    virtual bool NativeOnHandleBackAction() override
    {
        CloseWidget();
        return true; // We handled the action
    }

    // Set focus target for gamepad navigation
    virtual UWidget* NativeGetDesiredFocusTarget() const override
    {
        if (MyButton && MyButton->GetVisibility() == ESlateVisibility::Visible)
        {
            return MyButton;
        }
        return Super::NativeGetDesiredFocusTarget();
    }

protected:
    UPROPERTY(meta = (BindWidget))
    UCommonButtonBase* MyButton; // Use CommonUI widgets
};
```

### **Blueprint Events to Override**
- `BP On Handle Back Action` - Handle gamepad back button
- `BP On Widget Activated` - Widget activation logic
- `BP On Widget Deactivated` - Widget deactivation logic

### **CommonUI Widget Types to Use**
- `UCommonButtonBase` - Instead of regular buttons
- `UCommonTextBlock` - Instead of regular text blocks
- `UCommonBorder` - Instead of regular borders
- `UCommonActivatableWidget` - Base class for all UI widgets
- `UCommonActivatableWidgetStack` - For widget layer management

## Input Actions Reference

### **Standard UI Input Actions**
| Action | Gamepad | Keyboard | Purpose |
|--------|---------|----------|---------|
| UI_Confirm | A/X Button | Enter/Space | Confirm/Select |
| UI_Cancel | B/Circle Button | Escape/Backspace | Cancel/Back |
| UI_Navigate | D-Pad/Left Stick | Arrow Keys | Navigate UI |
| UI_TabLeft | Left Shoulder | Tab+Shift | Previous Tab |
| UI_TabRight | Right Shoulder | Tab | Next Tab |

### **Input Mapping Context Priorities**
- **UI Context**: Priority 100 (High)
- **Game Context**: Priority 0 (Normal)

## Widget Properties Reference

### **EnhancedUIWidgetBase Properties**
```cpp
// Enable automatic input helper
UPROPERTY(EditAnywhere, BlueprintReadWrite)
bool bUseAutomaticInputHelper = true;

// Enable automatic focus management
UPROPERTY(EditAnywhere, BlueprintReadWrite)
bool bUseAutomaticFocusManagement = true;

// Widget to focus when activated
UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (BindWidget))
UWidget* InitialFocusWidget;
```

### **ErrorWidgetBase Properties**
```cpp
// Required text blocks
UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
UTextBlock* ErrorHead;

UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
UTextBlock* ErrorBody;

// Optional buttons
UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
UCommonButtonBase* ConfirmButton;

UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
UCommonButtonBase* CancelButton;
```

## UIManager Integration

### **Adding Widgets to Stack**
```cpp
// Automatically handles input mapping contexts
UUIManager::AddWidgetToStack(
    this,                    // World context
    WidgetClass,            // Widget class
    EWidgetLayer::Menu,     // Layer
    EInputModeType::UIOnly, // Input mode (auto-switches contexts)
    true                    // Show cursor
);
```

### **Manual Input Mode Setting**
```cpp
// Also handles input mapping contexts
UUIManager::SetInputMode(
    this,                   // World context
    EInputModeType::UIOnly, // Input mode
    true                    // Show cursor
);
```

## Debugging Commands

### **Console Commands**
```
// Enable enhanced input logging
log LogEnhancedInput VeryVerbose

// Check input mapping contexts
showdebug enhancedinput
```

### **Debug Log Messages to Look For**
- `"Added UI Input Mapping Context"`
- `"Removed UI Input Mapping Context"`
- `"EnhancedUIWidgetBase: Constructing"`
- `"Error Widget: Closing"`
- `"UIManager Initialized"`

## Common Issues & Quick Fixes

| Issue | Quick Fix |
|-------|-----------|
| Gamepad not working in UI | Check UI input mapping context assignment |
| No focus on widget | Set `InitialFocusWidget` property |
| Input conflicts | Verify input action priorities |
| Error popup not showing | Check `ErrorWidgetClass` in CustomUISettings |
| Context not switching | Verify UIManager integration |

## File Locations

### **Key Files**
- `Source/StormEscape/Public/UI/EnhancedUIWidgetBase.h`
- `Source/StormEscape/Public/UI/UIInputHelper.h`
- `Source/StormEscape/Public/UI/ErrorWidgetBase.h`
- `Source/StormEscape/Public/UI/UIManager.h`
- `Source/StormEscape/Public/StormEscapePlayerController.h`

### **Input Assets**
- `Content/StormEscape/Input/Mappings/IM_UIControls.uasset`
- `Content/StormEscape/Input/IMC_FirstPersonMappingContext.uasset`

### **Configuration**
- Project Settings > Custom UI Settings > Error Widget Class
- StormEscapePlayerController Blueprint > Input section

## Migration Checklist

### **From Old Error Widget**
- [ ] Change base class to `UEnhancedUIWidgetBase`
- [ ] Remove manual input mode management
- [ ] Update `InitErrorWidget()` calls with new parameters
- [ ] Test gamepad functionality

### **From Old UI Widget**
- [ ] Change base class to `UEnhancedUIWidgetBase`
- [ ] Remove manual input binding code
- [ ] Override virtual input handlers
- [ ] Set `InitialFocusWidget`
- [ ] Test focus and navigation
