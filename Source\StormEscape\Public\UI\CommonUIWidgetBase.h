// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "CommonUIWidgetBase.generated.h"

/**
 * Base class for UI widgets that leverages CommonUI's native gamepad support
 * This class provides a foundation for widgets that need proper gamepad navigation
 * without reinventing CommonUI's wheel
 */
UCLASS(BlueprintType, Blueprintable)
class STORMESCAPE_API UCommonUIWidgetBase : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:
	UCommonUIWidgetBase(const FObjectInitializer& ObjectInitializer);

	// Enable automatic focus restoration when the widget is deactivated
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CommonUI Settings")
	bool bAutoRestoreFocus = true;

	// The widget to focus when this widget is activated (optional override)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CommonUI Settings", meta = (BindWidget))
	UWidget* DesiredFocusTarget;

	// Blueprint events for common input actions
	UFUNCTION(BlueprintImplementableEvent, Category = "CommonUI Events")
	bool BP_OnHandleBackAction();

	// Helper function to set focus to a specific widget
	UFUNCTION(BlueprintCallable, Category = "CommonUI")
	void SetFocusToWidget(UWidget* Widget);

	// Get the currently focused widget
	UFUNCTION(BlueprintPure, Category = "CommonUI")
	UWidget* GetCurrentlyFocusedWidget() const;

protected:
	virtual void NativeConstruct() override;
	virtual void NativeOnActivated() override;
	virtual void NativeOnDeactivated() override;

	// CommonUI back action handling - override this for custom behavior
	virtual bool NativeOnHandleBackAction() override;

	// CommonUI desired focus target - override this for custom focus behavior
	virtual UWidget* NativeGetDesiredFocusTarget() const override;

	// Called when the widget should set its initial focus
	UFUNCTION(BlueprintImplementableEvent, Category = "CommonUI Events")
	void BP_OnSetInitialFocus();

	// Called when the widget is activated
	UFUNCTION(BlueprintImplementableEvent, Category = "CommonUI Events")
	void BP_OnWidgetActivated();

	// Called when the widget is deactivated
	UFUNCTION(BlueprintImplementableEvent, Category = "CommonUI Events")
	void BP_OnWidgetDeactivated();

private:
	// Store the previously focused widget for restoration
	TWeakObjectPtr<UWidget> PreviouslyFocusedWidget;
};
