# CommonUI Gamepad Support Guide

This guide explains how to properly implement gamepad support using Unreal's native CommonUI system, which is the recommended approach for cross-platform UI development.

## Why CommonUI?

CommonUI is Unreal's official solution for cross-platform UI that handles:
- **Automatic gamepad navigation** between focusable widgets
- **Platform-specific input handling** (Xbox, PlayStation, Switch, etc.)
- **Input method switching** (mouse/keyboard ↔ gamepad)
- **Focus management** and visual feedback
- **Back action handling** for gamepad B/Circle button
- **Input routing** and action binding

Your StormEscape project already has CommonUI configured and working!

## Current CommonUI Configuration

Your project has these CommonUI components already set up:

### 1. CommonInput Settings (DefaultGame.ini)
```ini
[/Script/CommonInput.CommonInputSettings]
InputData=/Game/StormEscape/Input/Data/CUI_InputData.CUI_InputData_C

[CommonInputPlatformSettings_Windows CommonInputPlatformSettings]
DefaultInputType=MouseAndKeyboard
bSupportsMouseAndKeyboard=True
bSupportsGamepad=True
DefaultGamepadName=XSX
+ControllerData=/Game/StormEscape/UI/Foundation/Platform/Input/XGamepad/CommonInput_Gamepad_X.CommonInput_Gamepad_X_C
+ControllerData=/Game/StormEscape/UI/Foundation/Platform/Input/KeyboardMouse/CommonInput_KeyboardMouse.CommonInput_KeyboardMouse_C
```

### 2. Existing CommonUI Widgets
- `UBaseMainLayerUI` - Uses `UCommonActivatableWidget`
- `UCommonActivatableWidgetStack` - For widget layer management
- `UErrorWidgetBase` - Enhanced with CommonUI support

### 3. UIManager Integration
Your UIManager already works with CommonUI widgets and handles input mapping context switching.

## Enhanced Error Widget with CommonUI

The improved `UErrorWidgetBase` now uses CommonUI properly:

### Key CommonUI Features
```cpp
class UErrorWidgetBase : public UCommonActivatableWidget
{
    // CommonUI back action handling (B/Circle button)
    virtual bool NativeOnHandleBackAction() override;
    
    // CommonUI focus management
    virtual UWidget* NativeGetDesiredFocusTarget() const override;
    
    // Blueprint event for custom back handling
    UFUNCTION(BlueprintImplementableEvent)
    bool BP_OnHandleBackAction();
};
```

### Automatic Gamepad Support
- **Back button (B/Circle)** automatically calls `NativeOnHandleBackAction()`
- **Focus management** handled by `NativeGetDesiredFocusTarget()`
- **Navigation** between buttons works automatically
- **Visual feedback** provided by CommonUI styling

## Creating CommonUI Widgets

### 1. C++ Widget Class
```cpp
UCLASS()
class UMyMenuWidget : public UCommonActivatableWidget
{
    GENERATED_BODY()

public:
    // Handle gamepad back button
    virtual bool NativeOnHandleBackAction() override
    {
        // Custom back behavior
        CloseMenu();
        return true; // We handled it
    }

    // Set initial focus target
    virtual UWidget* NativeGetDesiredFocusTarget() const override
    {
        if (FirstButton && FirstButton->GetVisibility() == ESlateVisibility::Visible)
        {
            return FirstButton;
        }
        return Super::NativeGetDesiredFocusTarget();
    }

protected:
    UPROPERTY(meta = (BindWidget))
    UCommonButtonBase* FirstButton;
    
    UPROPERTY(meta = (BindWidget))
    UCommonButtonBase* SecondButton;
};
```

### 2. Blueprint Widget Setup
1. **Create Blueprint** based on `CommonActivatableWidget` or your C++ class
2. **Add CommonUI widgets**:
   - Use `CommonButtonBase` instead of regular buttons
   - Use `CommonTextBlock` for text
   - Use `CommonBorder` for containers
3. **Set up navigation**:
   - CommonUI handles navigation automatically between focusable widgets
   - Customize navigation rules if needed in the Navigation panel
4. **Handle back action**:
   - Override `BP On Handle Back Action` event
   - Return `true` if you handle the action, `false` to pass it up

### 3. Using with UIManager
```cpp
// Your existing UIManager code works perfectly with CommonUI
UCommonActivatableWidget* Widget = UUIManager::AddWidgetToStack(
    this,
    MyCommonUIWidgetClass,
    EWidgetLayer::Menu,
    EInputModeType::UIOnly,  // Automatically switches input mapping contexts
    true
);
```

## Input Mapping Context Integration

The enhanced system combines CommonUI with Enhanced Input:

### How It Works
1. **UIManager** switches input mapping contexts based on `EInputModeType`
2. **CommonUI** handles UI-specific input (navigation, back action)
3. **Enhanced Input** provides the underlying input actions
4. **Input mapping contexts** ensure proper gamepad input routing

### Input Flow
```
Gamepad Input → Enhanced Input → Input Mapping Context → CommonUI → Your Widget
```

### Configuration
Your input mapping contexts should include:
- **UI Context** (`IM_UIControls`): CommonUI navigation actions
- **Game Context** (`IMC_FirstPersonMappingContext`): Gameplay actions

## Best Practices

### 1. Widget Design
- **Always inherit** from `UCommonActivatableWidget`
- **Use CommonUI widgets** (CommonButtonBase, CommonTextBlock, etc.)
- **Set desired focus target** in `NativeGetDesiredFocusTarget()`
- **Handle back action** in `NativeOnHandleBackAction()`

### 2. Focus Management
```cpp
// Good: Let CommonUI handle focus
virtual UWidget* NativeGetDesiredFocusTarget() const override
{
    return MyFirstButton; // CommonUI will focus this automatically
}

// Avoid: Manual focus setting in NativeConstruct
// CommonUI handles this better through NativeGetDesiredFocusTarget()
```

### 3. Input Handling
```cpp
// Good: Use CommonUI back action
virtual bool NativeOnHandleBackAction() override
{
    CloseWidget();
    return true;
}

// Avoid: Custom input binding for back action
// CommonUI provides this automatically
```

### 4. Navigation
- **Let CommonUI handle navigation** between widgets automatically
- **Customize navigation** only when the automatic behavior isn't sufficient
- **Test with gamepad** to ensure navigation feels natural

## Testing Gamepad Support

### 1. Basic Testing
- Connect a gamepad (Xbox, PlayStation, etc.)
- Navigate UI using D-pad or left stick
- Test back button (B/Circle) functionality
- Verify focus visual feedback

### 2. Input Method Switching
- Start with mouse/keyboard
- Press a gamepad button → should switch to gamepad mode
- Move mouse → should switch back to mouse mode
- CommonUI handles this automatically

### 3. Platform Testing
- Test with different controller types
- Verify button prompts show correct icons
- Check that platform-specific behaviors work

## Migration from Custom Solution

If you have existing custom input handling:

### 1. Remove Custom Input Code
- Remove manual input action binding
- Remove custom navigation logic
- Remove manual focus management

### 2. Use CommonUI Methods
- Override `NativeOnHandleBackAction()` instead of custom input handlers
- Use `NativeGetDesiredFocusTarget()` instead of manual focus setting
- Let CommonUI handle navigation automatically

### 3. Update Widget Hierarchy
- Change base class to `UCommonActivatableWidget`
- Replace regular widgets with CommonUI equivalents
- Update Blueprint events to use CommonUI events

## Troubleshooting

### Common Issues
- **Navigation not working**: Ensure widgets are focusable and visible
- **Back button not working**: Check `NativeOnHandleBackAction()` implementation
- **Focus not setting**: Verify `NativeGetDesiredFocusTarget()` returns valid widget
- **Input conflicts**: Check input mapping context priorities

### Debug Tools
- **Widget Reflector**: Shows focus state and widget hierarchy
- **CommonUI Debug**: Use console command `CommonUI.DebugFocus 1`
- **Input Debug**: Use `showdebug input` for input routing

## Summary

CommonUI provides everything you need for gamepad support:
- ✅ **Automatic navigation** between focusable widgets
- ✅ **Back action handling** for gamepad B/Circle button  
- ✅ **Focus management** with visual feedback
- ✅ **Platform-specific input** handling
- ✅ **Input method switching** (mouse ↔ gamepad)
- ✅ **Integration** with your existing UIManager and input mapping contexts

The key is to leverage CommonUI's native capabilities instead of building custom solutions!
